/**
 * 视觉脚本文件操作节点
 * 提供文件加载、保存、检查等功能
 */
import { AsyncNode, AsyncNodeOptions } from '../nodes/AsyncNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType, NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 加载文件节点 (278)
 * 加载指定路径的文件
 */
export class LoadFileNode extends AsyncNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const asyncOptions: AsyncNodeOptions = {
      ...options,
      inputFlowName: 'load',
      outputFlowNames: ['success', 'error']
    };
    super(asyncOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'path',
      type: SocketType.DATA,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      dataType: 'string',
      description: '文件编码',
      defaultValue: 'utf-8'
    });

    this.addInput({
      name: 'asDataURL',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否作为DataURL读取',
      defaultValue: false
    });

    // 输出插槽
    this.addOutput({
      name: 'content',
      type: SocketType.DATA,
      dataType: 'string',
      description: '文件内容'
    });

    this.addOutput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      description: '文件大小（字节）'
    });

    this.addOutput({
      name: 'lastModified',
      type: SocketType.DATA,
      dataType: 'number',
      description: '最后修改时间'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @returns Promise<any>
   */
  protected async executeAsync(): Promise<any> {
    const path = this.getInputValue('path') as string;
    const encoding = this.getInputValue('encoding') as string;
    const asDataURL = this.getInputValue('asDataURL') as boolean;

    // 验证输入
    if (!path || typeof path !== 'string') {
      throw new Error('文件路径不能为空');
    }

    try {
      // 在浏览器环境中，我们使用File API
      if (typeof window !== 'undefined' && window.File) {
        const result = await this.loadFileInBrowser(path, encoding, asDataURL);
        
        // 设置输出值
        this.setOutputValue('content', result.content);
        this.setOutputValue('size', result.size);
        this.setOutputValue('lastModified', result.lastModified);

        // 触发成功流程
        this.triggerFlow('success');
        return result.content;
      } else {
        // 在Node.js环境中，我们使用fs模块
        const result = await this.loadFileInNode(path, encoding);
        
        // 设置输出值
        this.setOutputValue('content', result.content);
        this.setOutputValue('size', result.size);
        this.setOutputValue('lastModified', result.lastModified);

        // 触发成功流程
        this.triggerFlow('success');
        return result.content;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '文件加载失败';
      console.error('文件加载失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('content', '');
      this.setOutputValue('size', 0);
      this.setOutputValue('lastModified', 0);

      // 触发错误流程
      this.triggerFlow('error');
      throw error;
    }
  }

  /**
   * 在浏览器环境中加载文件
   * @param path 文件路径
   * @param encoding 编码
   * @param asDataURL 是否作为DataURL
   * @returns Promise<{content: string, size: number, lastModified: number}>
   */
  private async loadFileInBrowser(path: string, encoding: string, asDataURL: boolean): Promise<{content: string, size: number, lastModified: number}> {
    // 在浏览器中，通常通过fetch API或FileReader加载文件
    try {
      const response = await fetch(path);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      let content: string;
      if (asDataURL) {
        const blob = await response.blob();
        content = await this.blobToDataURL(blob);
      } else {
        content = await response.text();
      }

      return {
        content,
        size: content.length,
        lastModified: Date.now() // 浏览器中无法获取确切的修改时间
      };
    } catch (error) {
      throw new Error(`浏览器文件加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 在Node.js环境中加载文件
   * @param path 文件路径
   * @param encoding 编码
   * @returns Promise<{content: string, size: number, lastModified: number}>
   */
  private async loadFileInNode(path: string, encoding: string): Promise<{content: string, size: number, lastModified: number}> {
    // 模拟Node.js文件系统操作
    // 在实际实现中，这里应该使用fs模块
    console.log(`模拟Node.js文件加载: ${path}, 编码: ${encoding}`);
    
    // 模拟异步文件读取
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return {
      content: `模拟文件内容: ${path}`,
      size: 1024,
      lastModified: Date.now() - 86400000 // 1天前
    };
  }

  /**
   * 将Blob转换为DataURL
   * @param blob Blob对象
   * @returns Promise<string>
   */
  private blobToDataURL(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }
}

/**
 * 保存文件节点 (279)
 * 保存数据到文件
 */
export class SaveFileNode extends AsyncNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const asyncOptions: AsyncNodeOptions = {
      ...options,
      inputFlowName: 'save',
      outputFlowNames: ['success', 'error']
    };
    super(asyncOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'path',
      type: SocketType.DATA,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'content',
      type: SocketType.DATA,
      dataType: 'string',
      description: '文件内容',
      defaultValue: ''
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      dataType: 'string',
      description: '文件编码',
      defaultValue: 'utf-8'
    });

    this.addInput({
      name: 'createDir',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否创建目录',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'path',
      type: SocketType.DATA,
      dataType: 'string',
      description: '保存的文件路径'
    });

    this.addOutput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      description: '保存的文件大小'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '保存是否成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @returns Promise<any>
   */
  protected async executeAsync(): Promise<any> {
    const path = this.getInputValue('path') as string;
    const content = this.getInputValue('content') as string;
    const encoding = this.getInputValue('encoding') as string;
    const createDir = this.getInputValue('createDir') as boolean;

    // 验证输入
    if (!path || typeof path !== 'string') {
      throw new Error('文件路径不能为空');
    }

    if (content === null || content === undefined) {
      throw new Error('文件内容不能为空');
    }

    try {
      // 在浏览器环境中，我们使用下载API
      if (typeof window !== 'undefined') {
        const result = await this.saveFileInBrowser(path, content, encoding);
        
        // 设置输出值
        this.setOutputValue('path', path);
        this.setOutputValue('size', result.size);
        this.setOutputValue('success', true);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 在Node.js环境中，我们使用fs模块
        const result = await this.saveFileInNode(path, content, encoding, createDir);
        
        // 设置输出值
        this.setOutputValue('path', path);
        this.setOutputValue('size', result.size);
        this.setOutputValue('success', true);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '文件保存失败';
      console.error('文件保存失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('success', false);
      this.setOutputValue('size', 0);

      // 触发错误流程
      this.triggerFlow('error');
      throw error;
    }
  }

  /**
   * 在浏览器环境中保存文件
   * @param path 文件路径
   * @param content 内容
   * @param encoding 编码
   * @returns Promise<{size: number}>
   */
  private async saveFileInBrowser(path: string, content: string, encoding: string): Promise<{size: number}> {
    // 在浏览器中，我们创建下载链接
    const blob = new Blob([content], { type: 'text/plain;charset=' + encoding });
    const url = URL.createObjectURL(blob);
    
    // 创建下载链接
    const a = document.createElement('a');
    a.href = url;
    a.download = path.split('/').pop() || 'download.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    // 清理URL
    URL.revokeObjectURL(url);
    
    return {
      size: blob.size
    };
  }

  /**
   * 在Node.js环境中保存文件
   * @param path 文件路径
   * @param content 内容
   * @param encoding 编码
   * @param createDir 是否创建目录
   * @returns Promise<{size: number}>
   */
  private async saveFileInNode(path: string, content: string, encoding: string, createDir: boolean): Promise<{size: number}> {
    // 模拟Node.js文件系统操作
    console.log(`模拟Node.js文件保存: ${path}, 编码: ${encoding}, 创建目录: ${createDir}`);
    
    // 模拟异步文件写入
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return {
      size: content.length
    };
  }
}

/**
 * 文件存在检查节点 (280)
 * 检查文件是否存在
 */
export class FileExistsNode extends FunctionNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'path',
      type: SocketType.DATA,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    // 输出插槽
    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '文件是否存在'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const path = this.getInputValue('path') as string;

    // 验证输入
    if (!path || typeof path !== 'string') {
      this.setOutputValue('error', '文件路径不能为空');
      this.setOutputValue('exists', false);
      return false;
    }

    try {
      // 在浏览器环境中，我们无法直接检查文件是否存在
      // 这里提供一个模拟实现
      const exists = this.checkFileExists(path);

      // 设置输出值
      this.setOutputValue('exists', exists);
      this.setOutputValue('error', '');

      return exists;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '文件检查失败';
      console.error('文件存在检查失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('exists', false);

      return false;
    }
  }

  /**
   * 检查文件是否存在
   * @param path 文件路径
   * @returns boolean
   */
  private checkFileExists(path: string): boolean {
    // 模拟文件存在检查
    // 在实际实现中，这里应该使用适当的API
    console.log(`模拟文件存在检查: ${path}`);

    // 简单的模拟逻辑：包含特定关键词的路径认为存在
    return path.includes('test') || path.includes('example') || path.includes('demo');
  }
}

/**
 * 获取文件大小节点 (281)
 * 获取文件的大小
 */
export class GetFileSizeNode extends AsyncNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const asyncOptions: AsyncNodeOptions = {
      ...options,
      inputFlowName: 'get',
      outputFlowNames: ['success', 'error']
    };
    super(asyncOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'path',
      type: SocketType.DATA,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    // 输出插槽
    this.addOutput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      description: '文件大小（字节）'
    });

    this.addOutput({
      name: 'sizeKB',
      type: SocketType.DATA,
      dataType: 'number',
      description: '文件大小（KB）'
    });

    this.addOutput({
      name: 'sizeMB',
      type: SocketType.DATA,
      dataType: 'number',
      description: '文件大小（MB）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @returns Promise<any>
   */
  protected async executeAsync(): Promise<any> {
    const path = this.getInputValue('path') as string;

    // 验证输入
    if (!path || typeof path !== 'string') {
      throw new Error('文件路径不能为空');
    }

    try {
      // 获取文件大小
      const size = await this.getFileSize(path);

      // 计算不同单位的大小
      const sizeKB = Math.round((size / 1024) * 100) / 100;
      const sizeMB = Math.round((size / (1024 * 1024)) * 100) / 100;

      // 设置输出值
      this.setOutputValue('size', size);
      this.setOutputValue('sizeKB', sizeKB);
      this.setOutputValue('sizeMB', sizeMB);

      // 触发成功流程
      this.triggerFlow('success');
      return size;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取文件大小失败';
      console.error('获取文件大小失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('size', 0);
      this.setOutputValue('sizeKB', 0);
      this.setOutputValue('sizeMB', 0);

      // 触发错误流程
      this.triggerFlow('error');
      throw error;
    }
  }

  /**
   * 获取文件大小
   * @param path 文件路径
   * @returns Promise<number>
   */
  private async getFileSize(path: string): Promise<number> {
    // 模拟获取文件大小
    console.log(`模拟获取文件大小: ${path}`);

    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 50));

    // 返回模拟的文件大小
    return Math.floor(Math.random() * 1024 * 1024) + 1024; // 1KB到1MB之间
  }
}

/**
 * 获取文件扩展名节点 (282)
 * 获取文件的扩展名
 */
export class GetFileExtensionNode extends FunctionNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'path',
      type: SocketType.DATA,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'includeDot',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否包含点号',
      defaultValue: false
    });

    // 输出插槽
    this.addOutput({
      name: 'extension',
      type: SocketType.DATA,
      dataType: 'string',
      description: '文件扩展名'
    });

    this.addOutput({
      name: 'filename',
      type: SocketType.DATA,
      dataType: 'string',
      description: '文件名（不含扩展名）'
    });

    this.addOutput({
      name: 'basename',
      type: SocketType.DATA,
      dataType: 'string',
      description: '完整文件名'
    });

    this.addOutput({
      name: 'dirname',
      type: SocketType.DATA,
      dataType: 'string',
      description: '目录路径'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const path = this.getInputValue('path') as string;
    const includeDot = this.getInputValue('includeDot') as boolean;

    // 验证输入
    if (!path || typeof path !== 'string') {
      this.setOutputValue('extension', '');
      this.setOutputValue('filename', '');
      this.setOutputValue('basename', '');
      this.setOutputValue('dirname', '');
      return '';
    }

    try {
      // 解析文件路径
      const pathInfo = this.parsePath(path);

      // 处理扩展名
      let extension = pathInfo.extension;
      if (!includeDot && extension.startsWith('.')) {
        extension = extension.substring(1);
      } else if (includeDot && !extension.startsWith('.') && extension) {
        extension = '.' + extension;
      }

      // 设置输出值
      this.setOutputValue('extension', extension);
      this.setOutputValue('filename', pathInfo.filename);
      this.setOutputValue('basename', pathInfo.basename);
      this.setOutputValue('dirname', pathInfo.dirname);

      return extension;
    } catch (error) {
      console.error('获取文件扩展名失败:', error);

      // 设置默认输出
      this.setOutputValue('extension', '');
      this.setOutputValue('filename', '');
      this.setOutputValue('basename', '');
      this.setOutputValue('dirname', '');

      return '';
    }
  }

  /**
   * 解析文件路径
   * @param path 文件路径
   * @returns 路径信息
   */
  private parsePath(path: string): {extension: string, filename: string, basename: string, dirname: string} {
    // 标准化路径分隔符
    const normalizedPath = path.replace(/\\/g, '/');

    // 获取目录和文件名
    const lastSlashIndex = normalizedPath.lastIndexOf('/');
    const dirname = lastSlashIndex >= 0 ? normalizedPath.substring(0, lastSlashIndex) : '';
    const basename = lastSlashIndex >= 0 ? normalizedPath.substring(lastSlashIndex + 1) : normalizedPath;

    // 获取扩展名
    const lastDotIndex = basename.lastIndexOf('.');
    const extension = lastDotIndex >= 0 ? basename.substring(lastDotIndex) : '';
    const filename = lastDotIndex >= 0 ? basename.substring(0, lastDotIndex) : basename;

    return {
      extension,
      filename,
      basename,
      dirname
    };
  }
}

/**
 * 注册文件操作节点
 * @param registry 节点注册表
 */
export function registerFileNodes(registry: NodeRegistry): void {
  // 注册加载文件节点
  registry.registerNodeType({
    type: 'file/load',
    category: NodeCategory.DATA,
    constructor: LoadFileNode,
    label: '加载文件',
    description: '加载指定路径的文件',
    icon: 'file',
    color: '#607D8B',
    tags: ['file', 'load', 'read', 'data']
  });

  // 注册保存文件节点
  registry.registerNodeType({
    type: 'file/save',
    category: NodeCategory.DATA,
    constructor: SaveFileNode,
    label: '保存文件',
    description: '保存数据到文件',
    icon: 'save',
    color: '#4CAF50',
    tags: ['file', 'save', 'write', 'data']
  });

  // 注册文件存在检查节点
  registry.registerNodeType({
    type: 'file/exists',
    category: NodeCategory.DATA,
    constructor: FileExistsNode,
    label: '文件存在检查',
    description: '检查文件是否存在',
    icon: 'search',
    color: '#FF9800',
    tags: ['file', 'exists', 'check', 'validate']
  });

  // 注册获取文件大小节点
  registry.registerNodeType({
    type: 'file/getSize',
    category: NodeCategory.DATA,
    constructor: GetFileSizeNode,
    label: '获取文件大小',
    description: '获取文件的大小',
    icon: 'info',
    color: '#2196F3',
    tags: ['file', 'size', 'info', 'data']
  });

  // 注册获取文件扩展名节点
  registry.registerNodeType({
    type: 'file/getExtension',
    category: NodeCategory.DATA,
    constructor: GetFileExtensionNode,
    label: '获取文件扩展名',
    description: '获取文件的扩展名',
    icon: 'tag',
    color: '#9C27B0',
    tags: ['file', 'extension', 'parse', 'info']
  });

  console.log('已注册所有文件操作节点类型');
}
