/**
 * 视觉脚本会话存储节点
 * 提供浏览器sessionStorage操作功能
 */
import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType, NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 会话存储设置节点 (272)
 * 在会话存储中设置数据
 */
export class SessionStorageSetNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['success', 'fail']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      description: '存储的键名',
      defaultValue: ''
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      description: '要存储的值',
      defaultValue: null
    });

    this.addInput({
      name: 'serialize',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否序列化为JSON',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      description: '存储的键名'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      description: '存储的值'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '存储是否成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const key = this.getInputValue('key') as string;
    const value = this.getInputValue('value');
    const serialize = this.getInputValue('serialize') as boolean;

    // 验证输入
    if (!key || typeof key !== 'string') {
      this.setOutputValue('error', '键名必须是非空字符串');
      this.setOutputValue('success', false);
      this.triggerFlow('fail');
      return false;
    }

    // 检查sessionStorage是否可用
    if (typeof sessionStorage === 'undefined') {
      this.setOutputValue('error', 'sessionStorage不可用（可能在非浏览器环境中）');
      this.setOutputValue('success', false);
      this.triggerFlow('fail');
      return false;
    }

    try {
      let valueToStore: string;

      // 根据serialize参数决定是否序列化
      if (serialize && typeof value !== 'string') {
        valueToStore = JSON.stringify(value);
      } else {
        valueToStore = String(value);
      }

      // 存储到sessionStorage
      sessionStorage.setItem(key, valueToStore);

      // 设置输出值
      this.setOutputValue('key', key);
      this.setOutputValue('value', value);
      this.setOutputValue('success', true);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知存储错误';
      console.error('会话存储设置失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('success', false);

      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 会话存储获取节点 (273)
 * 从会话存储中获取数据
 */
export class SessionStorageGetNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['found', 'notFound']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      description: '要获取的键名',
      defaultValue: ''
    });

    this.addInput({
      name: 'defaultValue',
      type: SocketType.DATA,
      dataType: 'any',
      description: '默认值（键不存在时返回）',
      defaultValue: null
    });

    this.addInput({
      name: 'deserialize',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否反序列化JSON',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      description: '获取的值'
    });

    this.addOutput({
      name: 'rawValue',
      type: SocketType.DATA,
      dataType: 'string',
      description: '原始字符串值'
    });

    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '键是否存在'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const key = this.getInputValue('key') as string;
    const defaultValue = this.getInputValue('defaultValue');
    const deserialize = this.getInputValue('deserialize') as boolean;

    // 验证输入
    if (!key || typeof key !== 'string') {
      this.setOutputValue('error', '键名必须是非空字符串');
      this.setOutputValue('exists', false);
      this.setOutputValue('value', defaultValue);
      this.triggerFlow('notFound');
      return defaultValue;
    }

    // 检查sessionStorage是否可用
    if (typeof sessionStorage === 'undefined') {
      this.setOutputValue('error', 'sessionStorage不可用（可能在非浏览器环境中）');
      this.setOutputValue('exists', false);
      this.setOutputValue('value', defaultValue);
      this.triggerFlow('notFound');
      return defaultValue;
    }

    try {
      // 从sessionStorage获取值
      const rawValue = sessionStorage.getItem(key);

      if (rawValue === null) {
        // 键不存在
        this.setOutputValue('exists', false);
        this.setOutputValue('value', defaultValue);
        this.setOutputValue('rawValue', '');
        this.triggerFlow('notFound');
        return defaultValue;
      }

      let parsedValue: any = rawValue;

      // 根据deserialize参数决定是否反序列化
      if (deserialize) {
        try {
          parsedValue = JSON.parse(rawValue);
        } catch {
          // 如果JSON解析失败，返回原始字符串
          parsedValue = rawValue;
        }
      }

      // 设置输出值
      this.setOutputValue('value', parsedValue);
      this.setOutputValue('rawValue', rawValue);
      this.setOutputValue('exists', true);

      // 触发找到流程
      this.triggerFlow('found');
      return parsedValue;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知获取错误';
      console.error('会话存储获取失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('exists', false);
      this.setOutputValue('value', defaultValue);
      this.setOutputValue('rawValue', '');

      // 触发未找到流程
      this.triggerFlow('notFound');
      return defaultValue;
    }
  }
}

/**
 * 注册会话存储节点
 * @param registry 节点注册表
 */
export function registerSessionStorageNodes(registry: NodeRegistry): void {
  // 注册会话存储设置节点
  registry.registerNodeType({
    type: 'data/sessionStorage/set',
    category: NodeCategory.DATA,
    constructor: SessionStorageSetNode,
    label: '会话存储设置',
    description: '在会话存储中设置数据',
    icon: 'storage',
    color: '#FF9800',
    tags: ['data', 'storage', 'sessionStorage', 'set']
  });

  // 注册会话存储获取节点
  registry.registerNodeType({
    type: 'data/sessionStorage/get',
    category: NodeCategory.DATA,
    constructor: SessionStorageGetNode,
    label: '会话存储获取',
    description: '从会话存储中获取数据',
    icon: 'storage',
    color: '#FF9800',
    tags: ['data', 'storage', 'sessionStorage', 'get']
  });

  console.log('已注册所有会话存储节点类型');
}
