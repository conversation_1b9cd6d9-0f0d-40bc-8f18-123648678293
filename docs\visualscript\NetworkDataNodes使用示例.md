# 网络通信和数据处理节点使用示例

本文档提供了新实现的网络通信和数据处理节点（256-270）的详细使用示例和最佳实践。

## 目录

1. [HTTP请求节点](#http请求节点)
2. [WebSocket通信节点](#websocket通信节点)
3. [WebRTC数据通信节点](#webrtc数据通信节点)
4. [JSON数据处理节点](#json数据处理节点)
5. [本地存储节点](#本地存储节点)
6. [综合应用示例](#综合应用示例)

## HTTP请求节点

### 基本HTTP GET请求

```
[开始] → [HTTP GET请求] → [成功] → [显示结果]
                      ↓
                   [失败] → [显示错误]
```

**配置参数：**
- URL: `https://api.example.com/users`
- 请求头: `{"Authorization": "Bearer token123"}`
- 超时时间: `5000`毫秒

**输出数据：**
- 响应数据: 解析后的JSON对象或原始文本
- 状态码: HTTP状态码（如200、404等）
- 响应头: 服务器返回的响应头信息

### HTTP POST请求发送数据

```
[用户输入] → [JSON序列化] → [HTTP POST请求] → [处理响应]
```

**配置参数：**
- URL: `https://api.example.com/users`
- 请求体: `{"name": "张三", "email": "<EMAIL>"}`
- 请求头: `{"Content-Type": "application/json"}`

## WebSocket通信节点

### 建立WebSocket连接并发送消息

```
[开始] → [WebSocket连接] → [连接成功] → [WebSocket发送]
                        ↓
                    [连接失败] → [显示错误]

[WebSocket消息监听] → [收到消息] → [处理消息数据]
```

**连接配置：**
- URL: `ws://localhost:8080`
- 协议: `["chat", "notification"]`

**消息处理：**
- 发送消息: 自动序列化为JSON字符串
- 接收消息: 自动解析JSON或保持原始格式

### 实时聊天应用示例

```
[用户输入消息] → [WebSocket发送] → [发送成功]
                              ↓
                          [发送失败] → [重试机制]

[WebSocket消息监听] → [收到新消息] → [更新聊天界面]
```

## WebRTC数据通信节点

### 点对点数据传输

```
[开始] → [WebRTC连接] → [连接建立] → [WebRTC发送数据]
                     ↓
                 [连接失败] → [重新连接]

[WebRTC接收数据] → [数据到达] → [处理接收数据]
```

**连接配置：**
- 对等方ID: `peer1`
- ICE服务器: `[{"urls": "stun:stun.l.google.com:19302"}]`
- 数据通道标签: `dataChannel`

**数据传输：**
- 支持文本和二进制数据
- 低延迟点对点传输
- 自动连接状态管理

## JSON数据处理节点

### JSON解析和序列化

```
[JSON字符串] → [JSON解析] → [成功] → [使用对象数据]
                        ↓
                    [失败] → [处理解析错误]

[对象数据] → [JSON序列化] → [成功] → [发送JSON字符串]
                         ↓
                     [失败] → [处理序列化错误]
```

**解析示例：**
- 输入: `'{"name": "张三", "age": 25, "skills": ["JavaScript", "TypeScript"]}'`
- 输出: JavaScript对象
- 验证: `isValid` 输出为 `true`

**序列化示例：**
- 输入: `{name: "李四", age: 30}`
- 输出: `'{"name":"李四","age":30}'`
- 格式化: 启用 `prettyPrint` 可获得格式化输出

## 本地存储节点

### 数据持久化存储

```
[用户数据] → [本地存储设置] → [存储成功] → [确认保存]
                          ↓
                      [存储失败] → [显示错误]

[应用启动] → [本地存储获取] → [找到数据] → [恢复用户状态]
                          ↓
                      [未找到] → [使用默认值]
```

**存储配置：**
- 键名: `userPreferences`
- 数据: `{theme: "dark", language: "zh-CN"}`
- 序列化: `true`（自动转换为JSON）

**获取配置：**
- 键名: `userPreferences`
- 默认值: `{theme: "light", language: "en-US"}`
- 反序列化: `true`（自动解析JSON）

## 综合应用示例

### 在线协作应用

```
[用户登录] → [HTTP POST] → [获取Token] → [WebSocket连接]
                                        ↓
[本地存储Token] ← [存储成功] ← [JSON序列化] ← [连接成功]

[用户操作] → [JSON序列化] → [WebSocket发送] → [广播给其他用户]

[WebSocket消息] → [JSON解析] → [更新界面] → [本地存储状态]
```

### 文件分享应用

```
[选择文件] → [WebRTC连接] → [建立P2P连接] → [发送文件数据]
                                        ↓
[显示进度] ← [JSON解析] ← [WebSocket状态] ← [连接状态更新]

[接收端] → [WebRTC接收] → [文件重组] → [本地存储] → [下载完成]
```

### API数据管理应用

```
[用户请求] → [本地存储检查] → [缓存命中] → [返回缓存数据]
                          ↓
                      [缓存未命中] → [HTTP GET] → [JSON解析] → [本地存储] → [返回数据]
```

## 最佳实践

### 错误处理

1. **网络请求超时处理**
   - 设置合理的超时时间（5-10秒）
   - 提供重试机制
   - 显示友好的错误信息

2. **WebSocket连接管理**
   - 监听连接状态变化
   - 实现自动重连机制
   - 处理网络中断情况

3. **数据验证**
   - JSON解析前验证数据格式
   - 检查必要字段是否存在
   - 处理数据类型转换错误

### 性能优化

1. **缓存策略**
   - 使用本地存储缓存常用数据
   - 设置合理的缓存过期时间
   - 实现缓存更新机制

2. **数据传输优化**
   - 压缩大型JSON数据
   - 使用WebRTC传输大文件
   - 分批处理大量数据

3. **资源管理**
   - 及时关闭WebSocket连接
   - 清理WebRTC连接资源
   - 定期清理本地存储

### 安全考虑

1. **数据验证**
   - 验证所有外部输入数据
   - 防止JSON注入攻击
   - 限制数据大小和类型

2. **连接安全**
   - 使用HTTPS和WSS协议
   - 验证WebRTC连接身份
   - 加密敏感数据

3. **存储安全**
   - 不在本地存储敏感信息
   - 使用适当的数据加密
   - 定期清理过期数据

## 调试技巧

1. **使用调试日志节点**监控数据流
2. **检查网络面板**查看HTTP请求详情
3. **使用断点节点**暂停执行流程
4. **验证JSON格式**确保数据正确性
5. **监控存储使用量**避免超出限制

通过合理组合这些节点，您可以构建功能强大的网络应用和数据处理流程。
