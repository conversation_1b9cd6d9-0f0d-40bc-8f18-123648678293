/**
 * 视觉脚本本地存储节点
 * 提供浏览器localStorage操作功能
 */
import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType, NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 本地存储设置节点
 * 在本地存储中设置数据
 */
export class LocalStorageSetNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['success', 'fail']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加输入数据插槽
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '存储键名',
      defaultValue: 'myKey'
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要存储的值'
    });

    this.addInput({
      name: 'serialize',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否序列化为JSON',
      defaultValue: true,
      optional: true
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '存储的键名'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '存储的值'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const key = this.getInputValue('key') as string;
    const value = this.getInputValue('value');
    const serialize = this.getInputValue('serialize') as boolean;

    // 验证输入
    if (!key || typeof key !== 'string') {
      this.setOutputValue('error', '键名必须是非空字符串');
      this.setOutputValue('success', false);
      this.triggerFlow('fail');
      return false;
    }

    // 检查localStorage是否可用
    if (typeof localStorage === 'undefined') {
      this.setOutputValue('error', 'localStorage不可用（可能在非浏览器环境中）');
      this.setOutputValue('success', false);
      this.triggerFlow('fail');
      return false;
    }

    try {
      let valueToStore: string;

      // 根据serialize参数决定是否序列化
      if (serialize && typeof value !== 'string') {
        valueToStore = JSON.stringify(value);
      } else {
        valueToStore = String(value);
      }

      // 存储到localStorage
      localStorage.setItem(key, valueToStore);

      // 设置输出值
      this.setOutputValue('key', key);
      this.setOutputValue('value', value);
      this.setOutputValue('success', true);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知存储错误';
      console.error('本地存储设置失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('success', false);

      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 本地存储获取节点
 * 从本地存储中获取数据
 */
export class LocalStorageGetNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['found', 'notFound']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加输入数据插槽
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '存储键名',
      defaultValue: 'myKey'
    });

    this.addInput({
      name: 'defaultValue',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '默认值（键不存在时返回）',
      optional: true
    });

    this.addInput({
      name: 'deserialize',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否反序列化JSON',
      defaultValue: true,
      optional: true
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '获取的值'
    });

    this.addOutput({
      name: 'rawValue',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '原始字符串值'
    });

    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '键是否存在'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const key = this.getInputValue('key') as string;
    const defaultValue = this.getInputValue('defaultValue');
    const deserialize = this.getInputValue('deserialize') as boolean;

    // 验证输入
    if (!key || typeof key !== 'string') {
      this.setOutputValue('error', '键名必须是非空字符串');
      this.setOutputValue('exists', false);
      this.setOutputValue('value', defaultValue);
      this.triggerFlow('notFound');
      return defaultValue;
    }

    // 检查localStorage是否可用
    if (typeof localStorage === 'undefined') {
      this.setOutputValue('error', 'localStorage不可用（可能在非浏览器环境中）');
      this.setOutputValue('exists', false);
      this.setOutputValue('value', defaultValue);
      this.triggerFlow('notFound');
      return defaultValue;
    }

    try {
      // 从localStorage获取值
      const rawValue = localStorage.getItem(key);

      if (rawValue === null) {
        // 键不存在
        this.setOutputValue('exists', false);
        this.setOutputValue('value', defaultValue);
        this.setOutputValue('rawValue', '');
        this.triggerFlow('notFound');
        return defaultValue;
      }

      let parsedValue: any = rawValue;

      // 根据deserialize参数决定是否反序列化
      if (deserialize) {
        try {
          parsedValue = JSON.parse(rawValue);
        } catch {
          // 如果JSON解析失败，返回原始字符串
          parsedValue = rawValue;
        }
      }

      // 设置输出值
      this.setOutputValue('value', parsedValue);
      this.setOutputValue('rawValue', rawValue);
      this.setOutputValue('exists', true);

      // 触发找到流程
      this.triggerFlow('found');
      return parsedValue;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知获取错误';
      console.error('本地存储获取失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('exists', false);
      this.setOutputValue('value', defaultValue);
      this.setOutputValue('rawValue', '');

      // 触发未找到流程
      this.triggerFlow('notFound');
      return defaultValue;
    }
  }
}

/**
 * 本地存储删除节点 (271)
 * 从本地存储中删除数据
 */
export class LocalStorageRemoveNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['success', 'fail']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      description: '要删除的键名',
      defaultValue: ''
    });

    // 输出插槽
    this.addOutput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      description: '被删除的键名'
    });

    this.addOutput({
      name: 'existed',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '键是否存在'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '删除是否成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const key = this.getInputValue('key') as string;

    // 验证输入
    if (!key || typeof key !== 'string') {
      this.setOutputValue('error', '键名必须是非空字符串');
      this.setOutputValue('success', false);
      this.setOutputValue('existed', false);
      this.triggerFlow('fail');
      return false;
    }

    // 检查localStorage是否可用
    if (typeof localStorage === 'undefined') {
      this.setOutputValue('error', 'localStorage不可用（可能在非浏览器环境中）');
      this.setOutputValue('success', false);
      this.setOutputValue('existed', false);
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 检查键是否存在
      const existed = localStorage.getItem(key) !== null;

      // 删除键
      localStorage.removeItem(key);

      // 设置输出值
      this.setOutputValue('key', key);
      this.setOutputValue('existed', existed);
      this.setOutputValue('success', true);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知删除错误';
      console.error('本地存储删除失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('success', false);
      this.setOutputValue('existed', false);

      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册本地存储节点
 * @param registry 节点注册表
 */
export function registerLocalStorageNodes(registry: NodeRegistry): void {
  // 注册本地存储设置节点
  registry.registerNodeType({
    type: 'data/localStorage/set',
    category: NodeCategory.DATA,
    constructor: LocalStorageSetNode,
    label: '本地存储设置',
    description: '在本地存储中设置数据',
    icon: 'storage',
    color: '#FF9800',
    tags: ['data', 'storage', 'localStorage', 'set']
  });

  // 注册本地存储获取节点
  registry.registerNodeType({
    type: 'data/localStorage/get',
    category: NodeCategory.DATA,
    constructor: LocalStorageGetNode,
    label: '本地存储获取',
    description: '从本地存储中获取数据',
    icon: 'storage',
    color: '#FF9800',
    tags: ['data', 'storage', 'localStorage', 'get']
  });

  // 注册本地存储删除节点
  registry.registerNodeType({
    type: 'data/localStorage/remove',
    category: NodeCategory.DATA,
    constructor: LocalStorageRemoveNode,
    label: '本地存储删除',
    description: '从本地存储中删除数据',
    icon: 'delete',
    color: '#FF9800',
    tags: ['data', 'storage', 'localStorage', 'remove', 'delete']
  });

  console.log('已注册所有本地存储节点类型');
}
