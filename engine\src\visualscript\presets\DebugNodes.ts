/**
 * 视觉脚本调试节点
 * 提供调试、性能分析和监控功能
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 断点节点
 * 在执行到该节点时暂停执行
 */
export class BreakpointNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });
    
    // 添加输入数据插槽
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '断点条件',
      defaultValue: true
    });
    
    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '断点消息',
      defaultValue: ''
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const condition = this.getInputValue('condition') as boolean;
    const message = this.getInputValue('message') as string;
    
    // 如果条件为真，触发断点
    if (condition) {
      // 记录日志
      if (message) {
        console.log(`[断点] ${message}`);
      }

      // 注意：实际的断点功能需要通过调试器系统实现
      // 这里只是记录断点信息，实际暂停需要在执行引擎层面处理
      console.warn('[断点] 断点触发，但调试器未连接');
    }
    
    // 触发输出流程
    this.triggerFlow('flow');
    
    return null;
  }
}

/**
 * 日志节点
 * 输出日志信息
 */
export class LogNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });
    
    // 添加输入数据插槽
    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '日志消息'
    });
    
    this.addInput({
      name: 'level',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '日志级别',
      defaultValue: 'log'
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const message = this.getInputValue('message');
    const level = this.getInputValue('level') as string;
    
    // 根据日志级别输出日志
    switch (level) {
      case 'error':
        console.error(`[视觉脚本] ${message}`);
        break;
      case 'warn':
        console.warn(`[视觉脚本] ${message}`);
        break;
      case 'info':
        console.info(`[视觉脚本] ${message}`);
        break;
      case 'debug':
        console.debug(`[视觉脚本] ${message}`);
        break;
      default:
        console.log(`[视觉脚本] ${message}`);
        break;
    }
    
    // 触发输出流程
    this.triggerFlow('flow');
    
    return null;
  }
}

/**
 * 性能计时节点
 * 测量代码执行时间
 */
export class PerformanceTimerNode extends FlowNode {
  /** 开始时间 */
  private startTime: number = 0;
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始计时'
    });
    
    this.addInput({
      name: 'end',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '结束计时'
    });
    
    // 添加输入数据插槽
    this.addInput({
      name: 'label',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '计时标签',
      defaultValue: '性能计时'
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'startFlow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '开始计时后'
    });
    
    this.addOutput({
      name: 'endFlow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '结束计时后'
    });
    
    // 添加输出数据插槽
    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '持续时间（毫秒）'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const label = this.getInputValue('label') as string;

    // 检查哪个输入被触发
    const startTriggered = this.isInputTriggered('start');
    const endTriggered = this.isInputTriggered('end');

    if (startTriggered) {
      // 开始计时
      this.startTime = performance.now();

      // 触发开始流程
      this.triggerFlow('startFlow');

      return null;
    } else if (endTriggered) {
      // 结束计时
      const endTime = performance.now();
      const duration = endTime - this.startTime;

      // 输出计时结果
      console.log(`[性能计时] ${label}: ${duration.toFixed(2)}ms`);

      // 设置输出值
      this.setOutputValue('duration', duration);

      // 触发结束流程
      this.triggerFlow('endFlow');

      return duration;
    }

    return null;
  }

  /**
   * 检查输入是否被触发
   * @param inputName 输入名称
   * @returns 是否被触发
   */
  private isInputTriggered(inputName: string): boolean {
    // 这是一个简化的实现，实际应该通过执行上下文来判断
    // 这里假设如果输入存在连接就认为被触发了
    const input = this.inputs.get(inputName);
    return input ? !!input.connectedNodeId : false;
  }
}

/**
 * 变量监视节点
 * 监视变量的变化
 */
export class VariableWatchNode extends FlowNode {
  /** 上一个值 */
  private previousValue: any = undefined;
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });
    
    // 添加输入数据插槽
    this.addInput({
      name: 'variable',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要监视的变量'
    });
    
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '变量名称',
      defaultValue: '变量'
    });
    
    this.addInput({
      name: 'logChanges',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否记录变化',
      defaultValue: true
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });
    
    this.addOutput({
      name: 'changed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '变量变化时'
    });
    
    // 添加输出数据插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '变量值'
    });
    
    this.addOutput({
      name: 'previousValue',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '上一个值'
    });
    
    this.addOutput({
      name: 'hasChanged',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否变化'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const variable = this.getInputValue('variable');
    const name = this.getInputValue('name') as string;
    const logChanges = this.getInputValue('logChanges') as boolean;
    
    // 检查变量是否变化
    const hasChanged = !this.areValuesEqual(variable, this.previousValue);
    
    // 设置输出值
    this.setOutputValue('value', variable);
    this.setOutputValue('previousValue', this.previousValue);
    this.setOutputValue('hasChanged', hasChanged);
    
    // 如果变量变化且需要记录变化，输出日志
    if (hasChanged && logChanges) {
      console.log(`[变量监视] ${name} 变化:`, {
        from: this.previousValue,
        to: variable
      });
      
      // 触发变化流程
      this.triggerFlow('changed');
    }
    
    // 更新上一个值
    this.previousValue = this.cloneValue(variable);
    
    // 触发输出流程
    this.triggerFlow('flow');
    
    return variable;
  }
  
  /**
   * 比较两个值是否相等
   * @param a 第一个值
   * @param b 第二个值
   * @returns 是否相等
   */
  private areValuesEqual(a: any, b: any): boolean {
    // 如果两个值都是对象或数组，使用JSON.stringify比较
    if (typeof a === 'object' && a !== null && typeof b === 'object' && b !== null) {
      try {
        return JSON.stringify(a) === JSON.stringify(b);
      } catch (error) {
        // 如果JSON.stringify失败，使用简单比较
        return a === b;
      }
    }
    
    // 否则使用简单比较
    return a === b;
  }
  
  /**
   * 克隆值
   * @param value 要克隆的值
   * @returns 克隆后的值
   */
  private cloneValue(value: any): any {
    if (typeof value === 'object' && value !== null) {
      try {
        return JSON.parse(JSON.stringify(value));
      } catch (error) {
        // 如果JSON操作失败，返回原值
        return value;
      }
    }
    
    return value;
  }
}

/**
 * 断言节点
 * 验证条件是否为真
 */
export class AssertNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });
    
    // 添加输入数据插槽
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '断言条件'
    });
    
    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '断言消息',
      defaultValue: '断言失败'
    });
    
    this.addInput({
      name: 'throwError',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否抛出错误',
      defaultValue: false
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '断言成功'
    });
    
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '断言失败'
    });
    
    // 添加输出数据插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '断言结果'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const condition = this.getInputValue('condition') as boolean;
    const message = this.getInputValue('message') as string;
    const throwError = this.getInputValue('throwError') as boolean;
    
    // 设置输出值
    this.setOutputValue('result', condition);
    
    // 如果条件为真，断言成功
    if (condition) {
      // 触发成功流程
      this.triggerFlow('success');
    } else {
      // 断言失败
      console.error(`[断言] ${message}`);
      
      // 如果需要抛出错误，抛出错误
      if (throwError) {
        throw new Error(`断言失败: ${message}`);
      }
      
      // 触发失败流程
      this.triggerFlow('fail');
    }
    
    return condition;
  }
}

/**
 * 内存使用节点
 * 获取内存使用情况
 */
export class MemoryUsageNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'detailed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '详细信息',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'usedJSHeapSize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '已使用JS堆大小(字节)'
    });

    this.addOutput({
      name: 'totalJSHeapSize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '总JS堆大小(字节)'
    });

    this.addOutput({
      name: 'jsHeapSizeLimit',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: 'JS堆大小限制(字节)'
    });

    this.addOutput({
      name: 'memoryInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '内存信息对象'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const detailed = this.getInputValue('detailed') as boolean;

    // 获取内存使用情况
    const memoryInfo = this.getMemoryUsage(detailed);

    // 设置输出值
    this.setOutputValue('usedJSHeapSize', memoryInfo.usedJSHeapSize);
    this.setOutputValue('totalJSHeapSize', memoryInfo.totalJSHeapSize);
    this.setOutputValue('jsHeapSizeLimit', memoryInfo.jsHeapSizeLimit);
    this.setOutputValue('memoryInfo', memoryInfo);

    // 触发输出流程
    this.triggerFlow('flow');

    return memoryInfo;
  }

  /**
   * 获取内存使用情况
   * @param detailed 是否获取详细信息
   * @returns 内存信息
   */
  private getMemoryUsage(detailed: boolean): any {
    const memoryInfo: any = {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0,
      timestamp: Date.now()
    };

    // 检查是否支持performance.memory
    if ((performance as any).memory) {
      const memory = (performance as any).memory;
      memoryInfo.usedJSHeapSize = memory.usedJSHeapSize;
      memoryInfo.totalJSHeapSize = memory.totalJSHeapSize;
      memoryInfo.jsHeapSizeLimit = memory.jsHeapSizeLimit;
    } else {
      console.warn('内存使用节点: 浏览器不支持performance.memory API');
    }

    if (detailed) {
      // 添加详细信息
      memoryInfo.usagePercentage = memoryInfo.totalJSHeapSize > 0
        ? (memoryInfo.usedJSHeapSize / memoryInfo.totalJSHeapSize) * 100
        : 0;

      memoryInfo.freeMemory = memoryInfo.totalJSHeapSize - memoryInfo.usedJSHeapSize;

      // 格式化大小
      memoryInfo.formatted = {
        usedJSHeapSize: this.formatBytes(memoryInfo.usedJSHeapSize),
        totalJSHeapSize: this.formatBytes(memoryInfo.totalJSHeapSize),
        jsHeapSizeLimit: this.formatBytes(memoryInfo.jsHeapSizeLimit),
        freeMemory: this.formatBytes(memoryInfo.freeMemory)
      };
    }

    return memoryInfo;
  }

  /**
   * 格式化字节数
   * @param bytes 字节数
   * @returns 格式化的字符串
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

/**
 * 帧率监控节点 (301)
 * 监控当前帧率
 */
export class FrameRateNode extends FunctionNode {
  /** 帧率计算相关 */
  private frameCount: number = 0;
  private lastTime: number = 0;
  private currentFPS: number = 0;
  private isMonitoring: boolean = false;

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'enable',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      defaultValue: true,
      description: '是否启用监控'
    });

    // 输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'fps',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '当前帧率'
    });

    this.addOutput({
      name: 'averageFPS',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '平均帧率'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const enable = this.getInputValue('enable') as boolean;

    if (enable && !this.isMonitoring) {
      this.startMonitoring();
    } else if (!enable && this.isMonitoring) {
      this.stopMonitoring();
    }

    // 设置输出值
    this.setOutputValue('fps', this.currentFPS);
    this.setOutputValue('averageFPS', this.currentFPS); // 简化实现，实际可以计算真正的平均值

    // 触发输出流程
    this.triggerFlow('flow');

    return {
      fps: this.currentFPS,
      averageFPS: this.currentFPS
    };
  }

  /**
   * 开始监控帧率
   */
  private startMonitoring(): void {
    this.isMonitoring = true;
    this.lastTime = performance.now();
    this.frameCount = 0;
    this.updateFPS();
  }

  /**
   * 停止监控帧率
   */
  private stopMonitoring(): void {
    this.isMonitoring = false;
  }

  /**
   * 更新帧率计算
   */
  private updateFPS(): void {
    if (!this.isMonitoring) return;

    const currentTime = performance.now();
    this.frameCount++;

    // 每秒更新一次FPS
    if (currentTime - this.lastTime >= 1000) {
      this.currentFPS = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
      this.frameCount = 0;
      this.lastTime = currentTime;
    }

    // 继续监控
    requestAnimationFrame(() => this.updateFPS());
  }
}

/**
 * 绘制辅助线节点 (302)
 * 绘制调试辅助图形
 */
export class DrawGizmoNode extends FlowNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'type',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      defaultValue: 'line',
      description: '辅助线类型 (line, box, sphere, arrow)'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '位置'
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 1,
      description: '大小'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      defaultValue: '#FF0000',
      description: '颜色'
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 1,
      description: '显示时长（秒）'
    });

    // 输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'gizmoId',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '辅助图形ID'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const type = this.getInputValue('type') as string;
    const position = this.getInputValue('position') as { x: number, y: number, z: number };
    const size = this.getInputValue('size') as number;
    const color = this.getInputValue('color') as string;
    const duration = this.getInputValue('duration') as number;

    // 生成辅助图形ID
    const gizmoId = `gizmo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 绘制辅助图形（简化实现）
    this.drawGizmo(type, position, size, color, duration, gizmoId);

    // 设置输出值
    this.setOutputValue('gizmoId', gizmoId);

    // 触发输出流程
    this.triggerFlow('flow');

    return { gizmoId };
  }

  /**
   * 绘制辅助图形
   */
  private drawGizmo(
    type: string,
    position: { x: number, y: number, z: number },
    size: number,
    color: string,
    duration: number,
    gizmoId: string
  ): void {
    // 简化实现：在控制台输出调试信息
    console.log(`[调试辅助线] ${gizmoId}: 类型=${type}, 位置=(${position.x}, ${position.y}, ${position.z}), 大小=${size}, 颜色=${color}, 时长=${duration}秒`);

    // 实际实现中，这里应该调用渲染引擎的辅助图形绘制API
    // 例如：this.context.renderer.drawGizmo(type, position, size, color, duration);

    // 设置定时器，在指定时间后移除辅助图形
    if (duration > 0) {
      setTimeout(() => {
        console.log(`[调试辅助线] ${gizmoId}: 已移除`);
        // 实际实现中：this.context.renderer.removeGizmo(gizmoId);
      }, duration * 1000);
    }
  }
}

/**
 * 注册调试节点
 * @param registry 节点注册表
 */
export function registerDebugNodes(registry: NodeRegistry): void {
  // 注册断点节点
  registry.registerNodeType({
    type: 'debug/breakpoint',
    category: NodeCategory.DEBUG,
    constructor: BreakpointNode,
    label: '断点',
    description: '在执行到该节点时暂停执行',
    icon: 'breakpoint',
    color: '#F44336',
    tags: ['debug', 'breakpoint', 'pause']
  });

  // 注册日志节点
  registry.registerNodeType({
    type: 'debug/log',
    category: NodeCategory.DEBUG,
    constructor: LogNode,
    label: '日志',
    description: '输出日志信息',
    icon: 'log',
    color: '#F44336',
    tags: ['debug', 'log', 'console']
  });

  // 注册性能计时节点
  registry.registerNodeType({
    type: 'debug/performanceTimer',
    category: NodeCategory.DEBUG,
    constructor: PerformanceTimerNode,
    label: '性能计时',
    description: '测量代码执行时间',
    icon: 'timer',
    color: '#F44336',
    tags: ['debug', 'performance', 'timer']
  });

  // 注册变量监视节点
  registry.registerNodeType({
    type: 'debug/variableWatch',
    category: NodeCategory.DEBUG,
    constructor: VariableWatchNode,
    label: '变量监视',
    description: '监视变量的变化',
    icon: 'watch',
    color: '#F44336',
    tags: ['debug', 'variable', 'watch']
  });

  // 注册断言节点
  registry.registerNodeType({
    type: 'debug/assert',
    category: NodeCategory.DEBUG,
    constructor: AssertNode,
    label: '断言',
    description: '验证条件是否为真',
    icon: 'assert',
    color: '#F44336',
    tags: ['debug', 'assert', 'condition']
  });

  // 注册内存使用节点
  registry.registerNodeType({
    type: 'debug/memoryUsage',
    category: NodeCategory.DEBUG,
    constructor: MemoryUsageNode,
    label: '内存使用',
    description: '获取内存使用情况',
    icon: 'memory',
    color: '#F44336',
    tags: ['debug', 'memory', 'performance']
  });

  // 注册帧率监控节点 (301)
  registry.registerNodeType({
    type: 'debug/frameRate',
    category: NodeCategory.DEBUG,
    constructor: FrameRateNode,
    label: '帧率监控',
    description: '监控当前帧率',
    icon: 'monitor',
    color: '#F44336',
    tags: ['debug', 'fps', 'performance']
  });

  // 注册绘制辅助线节点 (302)
  registry.registerNodeType({
    type: 'debug/drawGizmo',
    category: NodeCategory.DEBUG,
    constructor: DrawGizmoNode,
    label: '绘制辅助线',
    description: '绘制调试辅助图形',
    icon: 'gizmo',
    color: '#F44336',
    tags: ['debug', 'gizmo', 'visual']
  });
}
