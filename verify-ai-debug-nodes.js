/**
 * 验证新实现的AI高级功能节点和调试工具节点
 * 节点286-300的验证脚本
 */

const fs = require('fs');
const path = require('path');

// 新增节点规格
const newNodes = [
  // AI功能节点 (286-295)
  { type: 'ai/pathfinding/findPath', name: '路径查找', category: 'AI功能' },
  { type: 'ai/pathfinding/followPath', name: '跟随路径', category: 'AI功能' },
  { type: 'ai/behavior/stateMachine', name: '状态机', category: 'AI功能' },
  { type: 'ai/behavior/decisionTree', name: '决策树', category: 'AI功能' },
  { type: 'ai/nlp/textAnalysis', name: '文本分析', category: 'AI功能' },
  { type: 'ai/nlp/speechToText', name: '语音转文本', category: 'AI功能' },
  { type: 'ai/nlp/textToSpeech', name: '文本转语音', category: 'AI功能' },
  { type: 'ai/vision/objectDetection', name: '物体检测', category: 'AI功能' },
  { type: 'ai/vision/faceRecognition', name: '人脸识别', category: 'AI功能' },
  { type: 'ai/animation/generateMotion', name: '生成动作', category: 'AI功能' },
  
  // 调试工具节点 (296-300)
  { type: 'debug/log', name: '调试日志', category: '调试工具' },
  { type: 'debug/breakpoint', name: '断点', category: '调试工具' },
  { type: 'debug/assert', name: '断言', category: '调试工具' },
  { type: 'debug/performanceTimer', name: '性能计时', category: '调试工具' },
  { type: 'debug/memoryUsage', name: '内存使用', category: '调试工具' }
];

async function verifyNodes() {
  console.log('=== 验证AI高级功能节点和调试工具节点 (286-300) ===\n');

  try {
    let successCount = 0;
    let failCount = 0;

    // 验证文件是否存在
    console.log('检查文件是否存在...');

    const filesToCheck = [
      'engine/src/visualscript/presets/AIAdvancedNodes.ts',
      'engine/src/visualscript/presets/DebugNodes.ts',
      'engine/src/visualscript/VisualScriptSystem.ts',
      'engine/src/visualscript/index.ts',
      'editor/src/components/scripting/VisualScriptEditor.tsx'
    ];

    for (const filePath of filesToCheck) {
      if (fs.existsSync(filePath)) {
        console.log(`  ✅ ${filePath} 存在`);
        successCount++;
      } else {
        console.log(`  ❌ ${filePath} 不存在`);
        failCount++;
      }
    }

    // 检查节点实现
    console.log('\n检查节点实现...');

    for (let i = 0; i < newNodes.length; i++) {
      const nodeSpec = newNodes[i];
      const nodeNumber = 286 + i;

      try {
        console.log(`验证节点 ${nodeNumber}: ${nodeSpec.name} (${nodeSpec.type})`);

        // 检查AI高级功能节点文件
        if (nodeSpec.category === 'AI功能') {
          const aiAdvancedFile = 'engine/src/visualscript/presets/AIAdvancedNodes.ts';
          if (fs.existsSync(aiAdvancedFile)) {
            const content = fs.readFileSync(aiAdvancedFile, 'utf8');

            // 检查节点类是否存在
            const nodeClassName = getNodeClassName(nodeSpec.type);
            if (content.includes(`class ${nodeClassName}`)) {
              console.log(`  ✅ 节点类 ${nodeClassName} 已实现`);

              // 检查注册代码
              if (content.includes(`type: '${nodeSpec.type}'`)) {
                console.log(`  ✅ 节点注册代码已添加`);
                successCount++;
              } else {
                console.log(`  ❌ 节点注册代码缺失`);
                failCount++;
              }
            } else {
              console.log(`  ❌ 节点类 ${nodeClassName} 未实现`);
              failCount++;
            }
          }
        }

        // 检查调试工具节点文件
        if (nodeSpec.category === '调试工具') {
          const debugFile = 'engine/src/visualscript/presets/DebugNodes.ts';
          if (fs.existsSync(debugFile)) {
            const content = fs.readFileSync(debugFile, 'utf8');

            // 检查节点类是否存在
            const nodeClassName = getNodeClassName(nodeSpec.type);
            if (content.includes(`class ${nodeClassName}`) || content.includes(`type: '${nodeSpec.type}'`)) {
              console.log(`  ✅ 节点 ${nodeClassName} 已实现或已存在`);
              successCount++;
            } else {
              console.log(`  ❌ 节点 ${nodeClassName} 未实现`);
              failCount++;
            }
          }
        }

        console.log('');
      } catch (error) {
        console.log(`  ❌ 验证失败: ${error.message}\n`);
        failCount++;
      }
    }
    
    // 检查编辑器集成
    console.log('检查编辑器集成...');
    const editorFile = 'editor/src/components/scripting/VisualScriptEditor.tsx';
    if (fs.existsSync(editorFile)) {
      const content = fs.readFileSync(editorFile, 'utf8');

      let editorSuccessCount = 0;
      newNodes.forEach(nodeSpec => {
        if (content.includes(`type: '${nodeSpec.type}'`)) {
          editorSuccessCount++;
        }
      });

      console.log(`  编辑器中已集成 ${editorSuccessCount}/${newNodes.length} 个节点`);
      successCount += editorSuccessCount;
    }

    // 输出验证结果
    console.log('\n=== 验证结果 ===');
    console.log(`总检查项: ${filesToCheck.length + newNodes.length * 2}`);
    console.log(`成功: ${successCount}`);
    console.log(`失败: ${failCount}`);

    if (failCount === 0) {
      console.log('\n🎉 所有验证项通过！');
    } else {
      console.log(`\n⚠️  有 ${failCount} 个验证项失败，请检查实现。`);
    }

    // 验证节点分类统计
    console.log('\n=== 节点分类统计 ===');
    const categories = {};
    newNodes.forEach(node => {
      categories[node.category] = (categories[node.category] || 0) + 1;
    });

    Object.entries(categories).forEach(([category, count]) => {
      console.log(`${category}: ${count} 个节点`);
    });

  } catch (error) {
    console.error('验证过程中发生错误:', error);
  }
}

// 根据节点类型获取节点类名
function getNodeClassName(nodeType) {
  // 特殊映射表
  const classNameMap = {
    'ai/pathfinding/findPath': 'PathfindingNode',
    'ai/pathfinding/followPath': 'FollowPathNode',
    'ai/behavior/stateMachine': 'AIStateMachineNode',
    'ai/behavior/decisionTree': 'AIDecisionTreeNode',
    'ai/nlp/textAnalysis': 'TextAnalysisNode',
    'ai/nlp/speechToText': 'SpeechToTextNode',
    'ai/nlp/textToSpeech': 'TextToSpeechNode',
    'ai/vision/objectDetection': 'ObjectDetectionNode',
    'ai/vision/faceRecognition': 'FaceRecognitionNode',
    'ai/animation/generateMotion': 'GenerateMotionNode',
    'debug/log': 'LogNode',
    'debug/breakpoint': 'BreakpointNode',
    'debug/assert': 'AssertNode',
    'debug/performanceTimer': 'PerformanceTimerNode',
    'debug/memoryUsage': 'MemoryUsageNode'
  };

  return classNameMap[nodeType] || 'UnknownNode';
}

// 运行验证
if (require.main === module) {
  verifyNodes().catch(console.error);
}

module.exports = { verifyNodes };
