/**
 * 视觉脚本数学节点
 * 提供数学运算相关的节点
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 三角函数节点类型
 */
enum TrigonometricType {
  SIN = 'sin',
  COS = 'cos',
  TAN = 'tan',
  ASIN = 'asin',
  ACOS = 'acos',
  ATAN = 'atan'
}

/**
 * 加法节点
 * 计算两个数的和
 */
export class AddNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 计算结果
    const result = a + b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 减法节点
 * 计算两个数的差
 */
export class SubtractNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 计算结果
    const result = a - b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 乘法节点
 * 计算两个数的积
 */
export class MultiplyNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 计算结果
    const result = a * b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 除法节点
 * 计算两个数的商
 */
export class DivideNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '被除数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '除数',
      defaultValue: 1
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 检查除数是否为0
    if (b === 0) {
      console.error('除数不能为0');
      this.triggerFlow('flow');
      return NaN;
    }

    // 计算结果
    const result = a / b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 取模节点
 * 计算两个数的模
 */
export class ModuloNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '被除数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '除数',
      defaultValue: 1
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 检查除数是否为0
    if (b === 0) {
      console.error('除数不能为0');
      this.triggerFlow('flow');
      return NaN;
    }

    // 计算结果
    const result = a % b;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 幂运算节点
 * 计算一个数的幂
 */
export class PowerNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加底数输入
    this.addInput({
      name: 'base',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '底数',
      defaultValue: 0
    });

    // 添加指数输入
    this.addInput({
      name: 'exponent',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '指数',
      defaultValue: 1
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const base = this.getInputValue('base') as number;
    const exponent = this.getInputValue('exponent') as number;

    // 计算结果
    const result = Math.pow(base, exponent);

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 平方根节点
 * 计算一个数的平方根
 */
export class SquareRootNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加数值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '数值',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const value = this.getInputValue('value') as number;

    // 检查输入值是否为负数
    if (value < 0) {
      console.error('不能计算负数的平方根');
      this.triggerFlow('flow');
      return NaN;
    }

    // 计算结果
    const result = Math.sqrt(value);

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 绝对值节点
 * 计算数的绝对值
 */
export class AbsoluteValueNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加数值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '数值',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '绝对值'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const value = this.getInputValue('value') as number;

    // 计算绝对值
    const result = Math.abs(value);

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 最小值节点
 * 获取多个数中的最小值
 */
export class MinNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '最小值'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 计算最小值
    const result = Math.min(a, b);

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 最大值节点
 * 获取多个数中的最大值
 */
export class MaxNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加第一个数输入
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });

    // 添加第二个数输入
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '最大值'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const a = this.getInputValue('a') as number;
    const b = this.getInputValue('b') as number;

    // 计算最大值
    const result = Math.max(a, b);

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 限制范围节点
 * 将数值限制在指定范围内
 */
export class ClampNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加数值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '数值',
      defaultValue: 0
    });

    // 添加最小值输入
    this.addInput({
      name: 'min',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '最小值',
      defaultValue: 0
    });

    // 添加最大值输入
    this.addInput({
      name: 'max',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '最大值',
      defaultValue: 1
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '限制后的值'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const value = this.getInputValue('value') as number;
    const min = this.getInputValue('min') as number;
    const max = this.getInputValue('max') as number;

    // 限制数值在范围内
    const result = Math.max(min, Math.min(max, value));

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 三角函数节点
 * 计算三角函数值
 */
export class TrigonometricNode extends FunctionNode {
  /** 三角函数类型 */
  private trigType: TrigonometricType;

  /**
   * 创建三角函数节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置三角函数类型
    this.trigType = options.metadata?.trigType || TrigonometricType.SIN;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加角度输入
    this.addInput({
      name: 'angle',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '角度（弧度）',
      defaultValue: 0
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const angle = this.getInputValue('angle') as number;

    // 根据三角函数类型计算结果
    let result: number;

    switch (this.trigType) {
      case TrigonometricType.SIN:
        result = Math.sin(angle);
        break;
      case TrigonometricType.COS:
        result = Math.cos(angle);
        break;
      case TrigonometricType.TAN:
        result = Math.tan(angle);
        break;
      case TrigonometricType.ASIN:
        result = Math.asin(angle);
        break;
      case TrigonometricType.ACOS:
        result = Math.acos(angle);
        break;
      case TrigonometricType.ATAN:
        result = Math.atan(angle);
        break;
      default:
        result = 0;
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 创建向量3节点 (303)
 * 创建三维向量
 */
export class CreateVector3Node extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'x',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0,
      description: 'X分量'
    });

    this.addInput({
      name: 'y',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0,
      description: 'Y分量'
    });

    this.addInput({
      name: 'z',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0,
      description: 'Z分量'
    });

    // 输出插槽
    this.addOutput({
      name: 'vector',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Vector3',
      description: '创建的向量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const x = this.getInputValue('x') as number;
    const y = this.getInputValue('y') as number;
    const z = this.getInputValue('z') as number;

    const vector = { x, y, z };
    this.setOutputValue('vector', vector);

    return vector;
  }
}

/**
 * 向量3加法节点 (304)
 * 计算两个三维向量的和
 */
export class Vector3AddNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'vectorA',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '向量A'
    });

    this.addInput({
      name: 'vectorB',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '向量B'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Vector3',
      description: '向量和'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const vectorA = this.getInputValue('vectorA') as { x: number, y: number, z: number };
    const vectorB = this.getInputValue('vectorB') as { x: number, y: number, z: number };

    const result = {
      x: vectorA.x + vectorB.x,
      y: vectorA.y + vectorB.y,
      z: vectorA.z + vectorB.z
    };

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 向量3减法节点 (305)
 * 计算两个三维向量的差
 */
export class Vector3SubtractNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'vectorA',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '向量A'
    });

    this.addInput({
      name: 'vectorB',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '向量B'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Vector3',
      description: '向量差'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const vectorA = this.getInputValue('vectorA') as { x: number, y: number, z: number };
    const vectorB = this.getInputValue('vectorB') as { x: number, y: number, z: number };

    const result = {
      x: vectorA.x - vectorB.x,
      y: vectorA.y - vectorB.y,
      z: vectorA.z - vectorB.z
    };

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 向量3乘法节点 (306)
 * 计算向量与标量的乘积
 */
export class Vector3MultiplyNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'vector',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '向量'
    });

    this.addInput({
      name: 'scalar',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 1,
      description: '标量'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Vector3',
      description: '乘积结果'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const vector = this.getInputValue('vector') as { x: number, y: number, z: number };
    const scalar = this.getInputValue('scalar') as number;

    const result = {
      x: vector.x * scalar,
      y: vector.y * scalar,
      z: vector.z * scalar
    };

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 向量3点积节点 (307)
 * 计算两个向量的点积
 */
export class Vector3DotNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'vectorA',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '向量A'
    });

    this.addInput({
      name: 'vectorB',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '向量B'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '点积结果'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const vectorA = this.getInputValue('vectorA') as { x: number, y: number, z: number };
    const vectorB = this.getInputValue('vectorB') as { x: number, y: number, z: number };

    const result = vectorA.x * vectorB.x + vectorA.y * vectorB.y + vectorA.z * vectorB.z;

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 向量3叉积节点 (308)
 * 计算两个向量的叉积
 */
export class Vector3CrossNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'vectorA',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '向量A'
    });

    this.addInput({
      name: 'vectorB',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '向量B'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Vector3',
      description: '叉积结果'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const vectorA = this.getInputValue('vectorA') as { x: number, y: number, z: number };
    const vectorB = this.getInputValue('vectorB') as { x: number, y: number, z: number };

    const result = {
      x: vectorA.y * vectorB.z - vectorA.z * vectorB.y,
      y: vectorA.z * vectorB.x - vectorA.x * vectorB.z,
      z: vectorA.x * vectorB.y - vectorA.y * vectorB.x
    };

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 向量3归一化节点 (309)
 * 将向量归一化
 */
export class Vector3NormalizeNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'vector',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '输入向量'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Vector3',
      description: '归一化向量'
    });

    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '原向量长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const vector = this.getInputValue('vector') as { x: number, y: number, z: number };

    const length = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);

    let result;
    if (length > 0) {
      result = {
        x: vector.x / length,
        y: vector.y / length,
        z: vector.z / length
      };
    } else {
      result = { x: 0, y: 0, z: 0 };
    }

    this.setOutputValue('result', result);
    this.setOutputValue('length', length);

    return { result, length };
  }
}

/**
 * 向量3距离节点 (310)
 * 计算两个向量间的距离
 */
export class Vector3DistanceNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'vectorA',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '向量A'
    });

    this.addInput({
      name: 'vectorB',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 0 },
      description: '向量B'
    });

    // 输出插槽
    this.addOutput({
      name: 'distance',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '距离'
    });

    this.addOutput({
      name: 'distanceSquared',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '距离的平方'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const vectorA = this.getInputValue('vectorA') as { x: number, y: number, z: number };
    const vectorB = this.getInputValue('vectorB') as { x: number, y: number, z: number };

    const dx = vectorB.x - vectorA.x;
    const dy = vectorB.y - vectorA.y;
    const dz = vectorB.z - vectorA.z;

    const distanceSquared = dx * dx + dy * dy + dz * dz;
    const distance = Math.sqrt(distanceSquared);

    this.setOutputValue('distance', distance);
    this.setOutputValue('distanceSquared', distanceSquared);

    return { distance, distanceSquared };
  }
}

/**
 * 创建四元数节点 (311)
 * 创建四元数
 */
export class CreateQuaternionNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'x',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0,
      description: 'X分量'
    });

    this.addInput({
      name: 'y',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0,
      description: 'Y分量'
    });

    this.addInput({
      name: 'z',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0,
      description: 'Z分量'
    });

    this.addInput({
      name: 'w',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 1,
      description: 'W分量'
    });

    // 输出插槽
    this.addOutput({
      name: 'quaternion',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Quaternion',
      description: '创建的四元数'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const x = this.getInputValue('x') as number;
    const y = this.getInputValue('y') as number;
    const z = this.getInputValue('z') as number;
    const w = this.getInputValue('w') as number;

    const quaternion = { x, y, z, w };
    this.setOutputValue('quaternion', quaternion);

    return quaternion;
  }
}

/**
 * 四元数乘法节点 (312)
 * 计算四元数乘法
 */
export class QuaternionMultiplyNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'quaternionA',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Quaternion',
      defaultValue: { x: 0, y: 0, z: 0, w: 1 },
      description: '四元数A'
    });

    this.addInput({
      name: 'quaternionB',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Quaternion',
      defaultValue: { x: 0, y: 0, z: 0, w: 1 },
      description: '四元数B'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Quaternion',
      description: '乘法结果'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const qA = this.getInputValue('quaternionA') as { x: number, y: number, z: number, w: number };
    const qB = this.getInputValue('quaternionB') as { x: number, y: number, z: number, w: number };

    // 四元数乘法公式
    const result = {
      x: qA.x * qB.w + qA.w * qB.x + qA.y * qB.z - qA.z * qB.y,
      y: qA.y * qB.w + qA.w * qB.y + qA.z * qB.x - qA.x * qB.z,
      z: qA.z * qB.w + qA.w * qB.z + qA.x * qB.y - qA.y * qB.x,
      w: qA.w * qB.w - qA.x * qB.x - qA.y * qB.y - qA.z * qB.z
    };

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 四元数插值节点 (313)
 * 四元数球面线性插值
 */
export class QuaternionSlerpNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'quaternionA',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Quaternion',
      defaultValue: { x: 0, y: 0, z: 0, w: 1 },
      description: '起始四元数'
    });

    this.addInput({
      name: 'quaternionB',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Quaternion',
      defaultValue: { x: 0, y: 0, z: 0, w: 1 },
      description: '目标四元数'
    });

    this.addInput({
      name: 't',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0.5,
      description: '插值参数 (0-1)'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Quaternion',
      description: '插值结果'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const qA = this.getInputValue('quaternionA') as { x: number, y: number, z: number, w: number };
    const qB = this.getInputValue('quaternionB') as { x: number, y: number, z: number, w: number };
    const t = Math.max(0, Math.min(1, this.getInputValue('t') as number)); // 限制在0-1范围

    // 计算点积
    let dot = qA.x * qB.x + qA.y * qB.y + qA.z * qB.z + qA.w * qB.w;

    // 如果点积为负，取反其中一个四元数以选择较短路径
    let qB2 = { ...qB };
    if (dot < 0) {
      qB2.x = -qB2.x;
      qB2.y = -qB2.y;
      qB2.z = -qB2.z;
      qB2.w = -qB2.w;
      dot = -dot;
    }

    // 如果四元数非常接近，使用线性插值
    if (dot > 0.9995) {
      const result = {
        x: qA.x + t * (qB2.x - qA.x),
        y: qA.y + t * (qB2.y - qA.y),
        z: qA.z + t * (qB2.z - qA.z),
        w: qA.w + t * (qB2.w - qA.w)
      };

      // 归一化
      const length = Math.sqrt(result.x * result.x + result.y * result.y + result.z * result.z + result.w * result.w);
      if (length > 0) {
        result.x /= length;
        result.y /= length;
        result.z /= length;
        result.w /= length;
      }

      this.setOutputValue('result', result);
      return result;
    }

    // 球面线性插值
    const theta0 = Math.acos(Math.abs(dot));
    const sinTheta0 = Math.sin(theta0);
    const theta = theta0 * t;
    const sinTheta = Math.sin(theta);

    const s0 = Math.cos(theta) - dot * sinTheta / sinTheta0;
    const s1 = sinTheta / sinTheta0;

    const result = {
      x: s0 * qA.x + s1 * qB2.x,
      y: s0 * qA.y + s1 * qB2.y,
      z: s0 * qA.z + s1 * qB2.z,
      w: s0 * qA.w + s1 * qB2.w
    };

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 随机范围节点 (314)
 * 生成指定范围的随机数
 */
export class RandomRangeNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'min',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0,
      description: '最小值'
    });

    this.addInput({
      name: 'max',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 1,
      description: '最大值'
    });

    this.addInput({
      name: 'isInteger',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      defaultValue: false,
      description: '是否为整数'
    });

    // 输出插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '随机值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const min = this.getInputValue('min') as number;
    const max = this.getInputValue('max') as number;
    const isInteger = this.getInputValue('isInteger') as boolean;

    // 确保min <= max
    const actualMin = Math.min(min, max);
    const actualMax = Math.max(min, max);

    let value: number;

    if (isInteger) {
      // 生成整数随机数
      value = Math.floor(Math.random() * (actualMax - actualMin + 1)) + actualMin;
    } else {
      // 生成浮点数随机数
      value = Math.random() * (actualMax - actualMin) + actualMin;
    }

    this.setOutputValue('value', value);
    return value;
  }
}

/**
 * 随机布尔节点 (315)
 * 生成随机布尔值
 */
export class RandomBooleanNode extends FunctionNode {
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'probability',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0.5,
      description: '为true的概率 (0-1)'
    });

    // 输出插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '随机布尔值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const probability = Math.max(0, Math.min(1, this.getInputValue('probability') as number)); // 限制在0-1范围

    const value = Math.random() < probability;

    this.setOutputValue('value', value);
    return value;
  }
}

/**
 * 注册数学节点
 * @param registry 节点注册表
 */
export function registerMathNodes(registry: NodeRegistry): void {
  // 注册加法节点
  registry.registerNodeType({
    type: 'math/basic/add',
    category: NodeCategory.MATH,
    constructor: AddNode,
    label: '加法',
    description: '计算两个数的和',
    icon: 'plus',
    color: '#2196F3',
    tags: ['math', 'basic', 'add']
  });

  // 注册减法节点
  registry.registerNodeType({
    type: 'math/basic/subtract',
    category: NodeCategory.MATH,
    constructor: SubtractNode,
    label: '减法',
    description: '计算两个数的差',
    icon: 'minus',
    color: '#2196F3',
    tags: ['math', 'basic', 'subtract']
  });

  // 注册乘法节点
  registry.registerNodeType({
    type: 'math/basic/multiply',
    category: NodeCategory.MATH,
    constructor: MultiplyNode,
    label: '乘法',
    description: '计算两个数的积',
    icon: 'multiply',
    color: '#2196F3',
    tags: ['math', 'basic', 'multiply']
  });

  // 注册Vector3创建节点 (303)
  registry.registerNodeType({
    type: 'math/vector3/create',
    category: NodeCategory.MATH,
    constructor: CreateVector3Node,
    label: '创建向量3',
    description: '创建三维向量',
    icon: 'vector',
    color: '#2196F3',
    tags: ['math', 'vector3', 'create']
  });

  // 注册Vector3加法节点 (304)
  registry.registerNodeType({
    type: 'math/vector3/add',
    category: NodeCategory.MATH,
    constructor: Vector3AddNode,
    label: '向量3加法',
    description: '计算两个三维向量的和',
    icon: 'plus',
    color: '#2196F3',
    tags: ['math', 'vector3', 'add']
  });

  // 注册Vector3减法节点 (305)
  registry.registerNodeType({
    type: 'math/vector3/subtract',
    category: NodeCategory.MATH,
    constructor: Vector3SubtractNode,
    label: '向量3减法',
    description: '计算两个三维向量的差',
    icon: 'minus',
    color: '#2196F3',
    tags: ['math', 'vector3', 'subtract']
  });

  // 注册Vector3乘法节点 (306)
  registry.registerNodeType({
    type: 'math/vector3/multiply',
    category: NodeCategory.MATH,
    constructor: Vector3MultiplyNode,
    label: '向量3乘法',
    description: '计算向量与标量的乘积',
    icon: 'multiply',
    color: '#2196F3',
    tags: ['math', 'vector3', 'multiply']
  });

  // 注册Vector3点积节点 (307)
  registry.registerNodeType({
    type: 'math/vector3/dot',
    category: NodeCategory.MATH,
    constructor: Vector3DotNode,
    label: '向量3点积',
    description: '计算两个向量的点积',
    icon: 'dot',
    color: '#2196F3',
    tags: ['math', 'vector3', 'dot']
  });

  // 注册Vector3叉积节点 (308)
  registry.registerNodeType({
    type: 'math/vector3/cross',
    category: NodeCategory.MATH,
    constructor: Vector3CrossNode,
    label: '向量3叉积',
    description: '计算两个向量的叉积',
    icon: 'cross',
    color: '#2196F3',
    tags: ['math', 'vector3', 'cross']
  });

  // 注册Vector3归一化节点 (309)
  registry.registerNodeType({
    type: 'math/vector3/normalize',
    category: NodeCategory.MATH,
    constructor: Vector3NormalizeNode,
    label: '向量3归一化',
    description: '将向量归一化',
    icon: 'normalize',
    color: '#2196F3',
    tags: ['math', 'vector3', 'normalize']
  });

  // 注册Vector3距离节点 (310)
  registry.registerNodeType({
    type: 'math/vector3/distance',
    category: NodeCategory.MATH,
    constructor: Vector3DistanceNode,
    label: '向量3距离',
    description: '计算两个向量间的距离',
    icon: 'distance',
    color: '#2196F3',
    tags: ['math', 'vector3', 'distance']
  });

  // 注册创建四元数节点 (311)
  registry.registerNodeType({
    type: 'math/quaternion/create',
    category: NodeCategory.MATH,
    constructor: CreateQuaternionNode,
    label: '创建四元数',
    description: '创建四元数',
    icon: 'quaternion',
    color: '#2196F3',
    tags: ['math', 'quaternion', 'create']
  });

  // 注册四元数乘法节点 (312)
  registry.registerNodeType({
    type: 'math/quaternion/multiply',
    category: NodeCategory.MATH,
    constructor: QuaternionMultiplyNode,
    label: '四元数乘法',
    description: '计算四元数乘法',
    icon: 'multiply',
    color: '#2196F3',
    tags: ['math', 'quaternion', 'multiply']
  });

  // 注册四元数插值节点 (313)
  registry.registerNodeType({
    type: 'math/quaternion/slerp',
    category: NodeCategory.MATH,
    constructor: QuaternionSlerpNode,
    label: '四元数插值',
    description: '四元数球面线性插值',
    icon: 'interpolate',
    color: '#2196F3',
    tags: ['math', 'quaternion', 'slerp']
  });

  // 注册随机范围节点 (314)
  registry.registerNodeType({
    type: 'utility/random/range',
    category: NodeCategory.MATH,
    constructor: RandomRangeNode,
    label: '随机范围',
    description: '生成指定范围的随机数',
    icon: 'random',
    color: '#FF9800',
    tags: ['utility', 'random', 'range']
  });

  // 注册随机布尔节点 (315)
  registry.registerNodeType({
    type: 'utility/random/boolean',
    category: NodeCategory.MATH,
    constructor: RandomBooleanNode,
    label: '随机布尔',
    description: '生成随机布尔值',
    icon: 'random',
    color: '#FF9800',
    tags: ['utility', 'random', 'boolean']
  });

  // 注册除法节点
  registry.registerNodeType({
    type: 'math/basic/divide',
    category: NodeCategory.MATH,
    constructor: DivideNode,
    label: '除法',
    description: '计算两个数的商',
    icon: 'divide',
    color: '#2196F3',
    tags: ['math', 'basic', 'divide']
  });

  // 注册取模节点
  registry.registerNodeType({
    type: 'math/basic/modulo',
    category: NodeCategory.MATH,
    constructor: ModuloNode,
    label: '取模',
    description: '计算两个数的模',
    icon: 'modulo',
    color: '#2196F3',
    tags: ['math', 'basic', 'modulo']
  });

  // 注册幂运算节点
  registry.registerNodeType({
    type: 'math/advanced/power',
    category: NodeCategory.MATH,
    constructor: PowerNode,
    label: '幂运算',
    description: '计算一个数的幂',
    icon: 'power',
    color: '#2196F3',
    tags: ['math', 'advanced', 'power']
  });

  // 注册平方根节点
  registry.registerNodeType({
    type: 'math/advanced/sqrt',
    category: NodeCategory.MATH,
    constructor: SquareRootNode,
    label: '平方根',
    description: '计算一个数的平方根',
    icon: 'sqrt',
    color: '#2196F3',
    tags: ['math', 'advanced', 'sqrt']
  });

  // 注册正弦函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/sin',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '正弦',
    description: '计算正弦值',
    icon: 'sin',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'sin'],
    metadata: {
      trigType: TrigonometricType.SIN
    }
  });

  // 注册余弦函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/cos',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '余弦',
    description: '计算余弦值',
    icon: 'cos',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'cos'],
    metadata: {
      trigType: TrigonometricType.COS
    }
  });

  // 注册正切函数节点
  registry.registerNodeType({
    type: 'math/trigonometric/tan',
    category: NodeCategory.MATH,
    constructor: TrigonometricNode,
    label: '正切',
    description: '计算正切值',
    icon: 'tan',
    color: '#2196F3',
    tags: ['math', 'trigonometric', 'tan'],
    metadata: {
      trigType: TrigonometricType.TAN
    }
  });

  // 注册绝对值节点
  registry.registerNodeType({
    type: 'math/advanced/abs',
    category: NodeCategory.MATH,
    constructor: AbsoluteValueNode,
    label: '绝对值',
    description: '计算数的绝对值',
    icon: 'abs',
    color: '#2196F3',
    tags: ['math', 'advanced', 'abs']
  });

  // 注册最小值节点
  registry.registerNodeType({
    type: 'math/advanced/min',
    category: NodeCategory.MATH,
    constructor: MinNode,
    label: '最小值',
    description: '获取多个数中的最小值',
    icon: 'min',
    color: '#2196F3',
    tags: ['math', 'advanced', 'min']
  });

  // 注册最大值节点
  registry.registerNodeType({
    type: 'math/advanced/max',
    category: NodeCategory.MATH,
    constructor: MaxNode,
    label: '最大值',
    description: '获取多个数中的最大值',
    icon: 'max',
    color: '#2196F3',
    tags: ['math', 'advanced', 'max']
  });

  // 注册限制范围节点
  registry.registerNodeType({
    type: 'math/advanced/clamp',
    category: NodeCategory.MATH,
    constructor: ClampNode,
    label: '限制范围',
    description: '将数值限制在指定范围内',
    icon: 'clamp',
    color: '#2196F3',
    tags: ['math', 'advanced', 'clamp']
  });
}
