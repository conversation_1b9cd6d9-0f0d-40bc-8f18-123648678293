/**
 * 网络通信和数据处理节点测试
 * 测试新实现的节点（256-270）功能
 */
import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { NodeRegistry } from '../../engine/src/visualscript/nodes/NodeRegistry';
import { 
  HTTPGetNode, 
  HTTPPostNode, 
  HTTPPutNode, 
  HTTPDeleteNode,
  registerHTTPNodes 
} from '../../engine/src/visualscript/presets/HTTPNodes';
import { 
  WebSocketConnectNode,
  WebSocketSendNode,
  WebSocketOnMessageNode,
  WebSocketDisconnectNode,
  registerWebSocketNodes 
} from '../../engine/src/visualscript/presets/WebSocketNodes';
import { 
  WebRTCCreateConnectionNode,
  WebRTCSendDataNode,
  WebRTCOnDataReceivedNode,
  registerWebRTCDataNodes 
} from '../../engine/src/visualscript/presets/WebRTCDataNodes';
import { 
  JSONParseNode,
  JSONStringifyNode,
  registerJSONNodes 
} from '../../engine/src/visualscript/presets/JSONNodes';
import { 
  LocalStorageSetNode,
  LocalStorageGetNode,
  registerLocalStorageNodes 
} from '../../engine/src/visualscript/presets/LocalStorageNodes';

describe('网络通信和数据处理节点测试', () => {
  let registry: NodeRegistry;

  beforeEach(() => {
    registry = new NodeRegistry();
    
    // 注册所有新节点
    registerHTTPNodes(registry);
    registerWebSocketNodes(registry);
    registerWebRTCDataNodes(registry);
    registerJSONNodes(registry);
    registerLocalStorageNodes(registry);
  });

  afterEach(() => {
    // 清理资源
    registry.clear();
  });

  describe('HTTP请求节点测试', () => {
    it('应该正确注册HTTP GET节点', () => {
      const nodeType = registry.getNodeType('network/http/get');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('HTTP GET请求');
    });

    it('应该正确注册HTTP POST节点', () => {
      const nodeType = registry.getNodeType('network/http/post');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('HTTP POST请求');
    });

    it('应该正确注册HTTP PUT节点', () => {
      const nodeType = registry.getNodeType('network/http/put');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('HTTP PUT请求');
    });

    it('应该正确注册HTTP DELETE节点', () => {
      const nodeType = registry.getNodeType('network/http/delete');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('HTTP DELETE请求');
    });

    it('HTTP GET节点应该有正确的输入输出插槽', () => {
      const node = new HTTPGetNode({ id: 'test-http-get', type: 'network/http/get' });
      
      // 检查输入插槽
      expect(node.getInput('flow')).toBeDefined();
      expect(node.getInput('url')).toBeDefined();
      expect(node.getInput('headers')).toBeDefined();
      expect(node.getInput('timeout')).toBeDefined();
      
      // 检查输出插槽
      expect(node.getOutput('success')).toBeDefined();
      expect(node.getOutput('fail')).toBeDefined();
      expect(node.getOutput('response')).toBeDefined();
      expect(node.getOutput('statusCode')).toBeDefined();
      expect(node.getOutput('headers')).toBeDefined();
      expect(node.getOutput('error')).toBeDefined();
    });
  });

  describe('WebSocket通信节点测试', () => {
    it('应该正确注册WebSocket连接节点', () => {
      const nodeType = registry.getNodeType('network/websocket/connect');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('WebSocket连接');
    });

    it('应该正确注册WebSocket发送节点', () => {
      const nodeType = registry.getNodeType('network/websocket/send');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('WebSocket发送');
    });

    it('应该正确注册WebSocket消息监听节点', () => {
      const nodeType = registry.getNodeType('network/websocket/onMessage');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('WebSocket消息');
    });

    it('应该正确注册WebSocket断开节点', () => {
      const nodeType = registry.getNodeType('network/websocket/disconnect');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('WebSocket断开');
    });
  });

  describe('WebRTC数据通信节点测试', () => {
    it('应该正确注册WebRTC连接创建节点', () => {
      const nodeType = registry.getNodeType('network/webrtc/createConnection');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('WebRTC连接');
    });

    it('应该正确注册WebRTC发送数据节点', () => {
      const nodeType = registry.getNodeType('network/webrtc/sendData');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('WebRTC发送数据');
    });

    it('应该正确注册WebRTC接收数据节点', () => {
      const nodeType = registry.getNodeType('network/webrtc/onDataReceived');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('WebRTC接收数据');
    });
  });

  describe('JSON数据处理节点测试', () => {
    it('应该正确注册JSON解析节点', () => {
      const nodeType = registry.getNodeType('data/json/parse');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('JSON解析');
    });

    it('应该正确注册JSON序列化节点', () => {
      const nodeType = registry.getNodeType('data/json/stringify');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('JSON序列化');
    });

    it('JSON解析节点应该正确解析JSON字符串', () => {
      const node = new JSONParseNode({ id: 'test-json-parse', type: 'data/json/parse' });
      
      // 设置输入值
      node.setInputValue('jsonString', '{"name": "test", "value": 123}');
      
      // 执行节点
      const result = node.execute();
      
      // 检查结果
      expect(result).toEqual({ name: 'test', value: 123 });
      expect(node.getOutputValue('isValid')).toBe(true);
    });

    it('JSON序列化节点应该正确序列化对象', () => {
      const node = new JSONStringifyNode({ id: 'test-json-stringify', type: 'data/json/stringify' });
      
      // 设置输入值
      node.setInputValue('value', { name: 'test', value: 123 });
      
      // 执行节点
      const result = node.execute();
      
      // 检查结果
      expect(result).toBe('{"name":"test","value":123}');
      expect(node.getOutputValue('length')).toBe(25);
    });
  });

  describe('本地存储节点测试', () => {
    it('应该正确注册本地存储设置节点', () => {
      const nodeType = registry.getNodeType('data/localStorage/set');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('本地存储设置');
    });

    it('应该正确注册本地存储获取节点', () => {
      const nodeType = registry.getNodeType('data/localStorage/get');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('本地存储获取');
    });

    // 注意：localStorage在Node.js测试环境中不可用，需要模拟
    it('本地存储节点应该有正确的输入输出插槽', () => {
      const setNode = new LocalStorageSetNode({ id: 'test-storage-set', type: 'data/localStorage/set' });
      const getNode = new LocalStorageGetNode({ id: 'test-storage-get', type: 'data/localStorage/get' });
      
      // 检查设置节点的插槽
      expect(setNode.getInput('key')).toBeDefined();
      expect(setNode.getInput('value')).toBeDefined();
      expect(setNode.getInput('serialize')).toBeDefined();
      expect(setNode.getOutput('success')).toBeDefined();
      expect(setNode.getOutput('error')).toBeDefined();
      
      // 检查获取节点的插槽
      expect(getNode.getInput('key')).toBeDefined();
      expect(getNode.getInput('defaultValue')).toBeDefined();
      expect(getNode.getInput('deserialize')).toBeDefined();
      expect(getNode.getOutput('value')).toBeDefined();
      expect(getNode.getOutput('exists')).toBeDefined();
    });
  });

  describe('节点分类测试', () => {
    it('所有网络节点应该属于NETWORK分类', () => {
      const httpGet = registry.getNodeType('network/http/get');
      const wsConnect = registry.getNodeType('network/websocket/connect');
      const webrtcCreate = registry.getNodeType('network/webrtc/createConnection');
      
      expect(httpGet?.category).toBe('network');
      expect(wsConnect?.category).toBe('network');
      expect(webrtcCreate?.category).toBe('network');
    });

    it('所有数据处理节点应该属于DATA分类', () => {
      const jsonParse = registry.getNodeType('data/json/parse');
      const localStorage = registry.getNodeType('data/localStorage/set');
      
      expect(jsonParse?.category).toBe('data');
      expect(localStorage?.category).toBe('data');
    });
  });

  describe('节点标签和描述测试', () => {
    it('所有节点应该有中文标签和描述', () => {
      const nodeTypes = [
        'network/http/get',
        'network/websocket/connect',
        'network/webrtc/createConnection',
        'data/json/parse',
        'data/localStorage/set'
      ];

      nodeTypes.forEach(type => {
        const nodeType = registry.getNodeType(type);
        expect(nodeType?.label).toBeDefined();
        expect(nodeType?.description).toBeDefined();
        expect(nodeType?.label).toMatch(/[\u4e00-\u9fa5]/); // 包含中文字符
      });
    });
  });
});
