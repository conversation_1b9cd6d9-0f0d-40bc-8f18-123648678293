/**
 * 数据存储节点测试
 * 测试新实现的数据存储、文件操作和资产管理节点
 */
import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { NodeRegistry } from '../../engine/src/visualscript/nodes/NodeRegistry';

// 导入新实现的节点
import { 
  LocalStorageRemoveNode,
  registerLocalStorageNodes 
} from '../../engine/src/visualscript/presets/LocalStorageNodes';
import { 
  SessionStorageSetNode,
  SessionStorageGetNode,
  registerSessionStorageNodes 
} from '../../engine/src/visualscript/presets/SessionStorageNodes';
import { 
  DatabaseQueryNode,
  DatabaseInsertNode,
  DatabaseUpdateNode,
  DatabaseDeleteNode,
  registerDatabaseNodes 
} from '../../engine/src/visualscript/presets/DatabaseNodes';
import { 
  LoadFileNode,
  SaveFileNode,
  FileExistsNode,
  GetFileSizeNode,
  GetFileExtensionNode,
  registerFileNodes 
} from '../../engine/src/visualscript/presets/FileNodes';
import { 
  LoadAssetNode,
  UnloadAssetNode,
  GetAssetProgressNode,
  registerAssetNodes 
} from '../../engine/src/visualscript/presets/AssetNodes';

describe('数据存储节点测试 (271-285)', () => {
  let registry: NodeRegistry;

  beforeEach(() => {
    registry = new NodeRegistry();
    
    // 注册所有新节点
    registerLocalStorageNodes(registry);
    registerSessionStorageNodes(registry);
    registerDatabaseNodes(registry);
    registerFileNodes(registry);
    registerAssetNodes(registry);
  });

  afterEach(() => {
    // 清理资源
    registry.clear();
  });

  describe('本地存储删除节点测试 (271)', () => {
    it('应该正确注册本地存储删除节点', () => {
      const nodeType = registry.getNodeType('data/localStorage/remove');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('本地存储删除');
    });

    it('本地存储删除节点应该有正确的输入输出插槽', () => {
      const node = new LocalStorageRemoveNode({ id: 'test-storage-remove', type: 'data/localStorage/remove' });
      
      // 检查输入插槽
      expect(node.getInput('key')).toBeDefined();
      
      // 检查输出插槽
      expect(node.getOutput('key')).toBeDefined();
      expect(node.getOutput('existed')).toBeDefined();
      expect(node.getOutput('success')).toBeDefined();
      expect(node.getOutput('error')).toBeDefined();
    });
  });

  describe('会话存储节点测试 (272-273)', () => {
    it('应该正确注册会话存储设置节点', () => {
      const nodeType = registry.getNodeType('data/sessionStorage/set');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('会话存储设置');
    });

    it('应该正确注册会话存储获取节点', () => {
      const nodeType = registry.getNodeType('data/sessionStorage/get');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('会话存储获取');
    });

    it('会话存储节点应该有正确的输入输出插槽', () => {
      const setNode = new SessionStorageSetNode({ id: 'test-session-set', type: 'data/sessionStorage/set' });
      const getNode = new SessionStorageGetNode({ id: 'test-session-get', type: 'data/sessionStorage/get' });
      
      // 检查设置节点的插槽
      expect(setNode.getInput('key')).toBeDefined();
      expect(setNode.getInput('value')).toBeDefined();
      expect(setNode.getInput('serialize')).toBeDefined();
      expect(setNode.getOutput('success')).toBeDefined();
      expect(setNode.getOutput('error')).toBeDefined();
      
      // 检查获取节点的插槽
      expect(getNode.getInput('key')).toBeDefined();
      expect(getNode.getInput('defaultValue')).toBeDefined();
      expect(getNode.getInput('deserialize')).toBeDefined();
      expect(getNode.getOutput('value')).toBeDefined();
      expect(getNode.getOutput('exists')).toBeDefined();
    });
  });

  describe('数据库操作节点测试 (274-277)', () => {
    it('应该正确注册所有数据库操作节点', () => {
      const queryNode = registry.getNodeType('data/database/query');
      const insertNode = registry.getNodeType('data/database/insert');
      const updateNode = registry.getNodeType('data/database/update');
      const deleteNode = registry.getNodeType('data/database/delete');
      
      expect(queryNode).toBeDefined();
      expect(insertNode).toBeDefined();
      expect(updateNode).toBeDefined();
      expect(deleteNode).toBeDefined();
      
      expect(queryNode?.label).toBe('数据库查询');
      expect(insertNode?.label).toBe('数据库插入');
      expect(updateNode?.label).toBe('数据库更新');
      expect(deleteNode?.label).toBe('数据库删除');
    });

    it('数据库节点应该有正确的输入输出插槽', () => {
      const queryNode = new DatabaseQueryNode({ id: 'test-db-query', type: 'data/database/query' });
      const insertNode = new DatabaseInsertNode({ id: 'test-db-insert', type: 'data/database/insert' });
      
      // 检查查询节点的插槽
      expect(queryNode.getInput('config')).toBeDefined();
      expect(queryNode.getInput('query')).toBeDefined();
      expect(queryNode.getInput('parameters')).toBeDefined();
      expect(queryNode.getOutput('result')).toBeDefined();
      expect(queryNode.getOutput('rowCount')).toBeDefined();
      
      // 检查插入节点的插槽
      expect(insertNode.getInput('config')).toBeDefined();
      expect(insertNode.getInput('table')).toBeDefined();
      expect(insertNode.getInput('data')).toBeDefined();
      expect(insertNode.getOutput('insertId')).toBeDefined();
      expect(insertNode.getOutput('affectedRows')).toBeDefined();
    });
  });

  describe('文件操作节点测试 (278-282)', () => {
    it('应该正确注册所有文件操作节点', () => {
      const loadNode = registry.getNodeType('file/load');
      const saveNode = registry.getNodeType('file/save');
      const existsNode = registry.getNodeType('file/exists');
      const sizeNode = registry.getNodeType('file/getSize');
      const extNode = registry.getNodeType('file/getExtension');
      
      expect(loadNode).toBeDefined();
      expect(saveNode).toBeDefined();
      expect(existsNode).toBeDefined();
      expect(sizeNode).toBeDefined();
      expect(extNode).toBeDefined();
      
      expect(loadNode?.label).toBe('加载文件');
      expect(saveNode?.label).toBe('保存文件');
      expect(existsNode?.label).toBe('文件存在检查');
      expect(sizeNode?.label).toBe('获取文件大小');
      expect(extNode?.label).toBe('获取文件扩展名');
    });

    it('文件操作节点应该有正确的输入输出插槽', () => {
      const loadNode = new LoadFileNode({ id: 'test-file-load', type: 'file/load' });
      const saveNode = new SaveFileNode({ id: 'test-file-save', type: 'file/save' });
      const existsNode = new FileExistsNode({ id: 'test-file-exists', type: 'file/exists' });
      const extNode = new GetFileExtensionNode({ id: 'test-file-ext', type: 'file/getExtension' });
      
      // 检查加载节点的插槽
      expect(loadNode.getInput('path')).toBeDefined();
      expect(loadNode.getInput('encoding')).toBeDefined();
      expect(loadNode.getOutput('content')).toBeDefined();
      expect(loadNode.getOutput('size')).toBeDefined();
      
      // 检查保存节点的插槽
      expect(saveNode.getInput('path')).toBeDefined();
      expect(saveNode.getInput('content')).toBeDefined();
      expect(saveNode.getOutput('success')).toBeDefined();
      
      // 检查存在检查节点的插槽
      expect(existsNode.getInput('path')).toBeDefined();
      expect(existsNode.getOutput('exists')).toBeDefined();
      
      // 检查扩展名节点的插槽
      expect(extNode.getInput('path')).toBeDefined();
      expect(extNode.getOutput('extension')).toBeDefined();
      expect(extNode.getOutput('filename')).toBeDefined();
      expect(extNode.getOutput('basename')).toBeDefined();
      expect(extNode.getOutput('dirname')).toBeDefined();
    });
  });

  describe('资产管理节点测试 (283-285)', () => {
    it('应该正确注册所有资产管理节点', () => {
      const loadNode = registry.getNodeType('asset/load');
      const unloadNode = registry.getNodeType('asset/unload');
      const progressNode = registry.getNodeType('asset/getProgress');
      
      expect(loadNode).toBeDefined();
      expect(unloadNode).toBeDefined();
      expect(progressNode).toBeDefined();
      
      expect(loadNode?.label).toBe('加载资产');
      expect(unloadNode?.label).toBe('卸载资产');
      expect(progressNode?.label).toBe('获取加载进度');
    });

    it('资产管理节点应该有正确的输入输出插槽', () => {
      const loadNode = new LoadAssetNode({ id: 'test-asset-load', type: 'asset/load' });
      const unloadNode = new UnloadAssetNode({ id: 'test-asset-unload', type: 'asset/unload' });
      const progressNode = new GetAssetProgressNode({ id: 'test-asset-progress', type: 'asset/getProgress' });
      
      // 检查加载节点的插槽
      expect(loadNode.getInput('path')).toBeDefined();
      expect(loadNode.getInput('type')).toBeDefined();
      expect(loadNode.getOutput('asset')).toBeDefined();
      expect(loadNode.getOutput('assetId')).toBeDefined();
      expect(loadNode.getOutput('size')).toBeDefined();
      
      // 检查卸载节点的插槽
      expect(unloadNode.getInput('assetId')).toBeDefined();
      expect(unloadNode.getOutput('unloaded')).toBeDefined();
      expect(unloadNode.getOutput('freedMemory')).toBeDefined();
      
      // 检查进度节点的插槽
      expect(progressNode.getInput('assetId')).toBeDefined();
      expect(progressNode.getOutput('progress')).toBeDefined();
      expect(progressNode.getOutput('loaded')).toBeDefined();
      expect(progressNode.getOutput('status')).toBeDefined();
    });
  });

  describe('节点分类测试', () => {
    it('所有新节点应该属于DATA分类', () => {
      const nodeTypes = [
        'data/localStorage/remove',
        'data/sessionStorage/set',
        'data/sessionStorage/get',
        'data/database/query',
        'data/database/insert',
        'data/database/update',
        'data/database/delete',
        'file/load',
        'file/save',
        'file/exists',
        'file/getSize',
        'file/getExtension',
        'asset/load',
        'asset/unload',
        'asset/getProgress'
      ];

      nodeTypes.forEach(type => {
        const nodeType = registry.getNodeType(type);
        expect(nodeType?.category).toBe('data');
      });
    });
  });

  describe('节点标签和描述测试', () => {
    it('所有节点应该有中文标签和描述', () => {
      const nodeTypes = [
        'data/localStorage/remove',
        'data/sessionStorage/set',
        'data/sessionStorage/get',
        'data/database/query',
        'file/load',
        'asset/load'
      ];

      nodeTypes.forEach(type => {
        const nodeType = registry.getNodeType(type);
        expect(nodeType?.label).toBeDefined();
        expect(nodeType?.description).toBeDefined();
        expect(nodeType?.label).toMatch(/[\u4e00-\u9fa5]/); // 包含中文字符
      });
    });
  });
});
