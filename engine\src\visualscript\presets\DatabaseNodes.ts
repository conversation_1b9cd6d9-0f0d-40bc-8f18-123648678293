/**
 * 视觉脚本数据库操作节点
 * 提供数据库查询、插入、更新、删除功能
 */
import { AsyncNode, AsyncNodeOptions } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType, NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 数据库连接配置接口
 */
export interface DatabaseConfig {
  /** 数据库类型 */
  type: 'mysql' | 'postgresql' | 'sqlite' | 'mongodb';
  /** 主机地址 */
  host?: string;
  /** 端口号 */
  port?: number;
  /** 数据库名 */
  database: string;
  /** 用户名 */
  username?: string;
  /** 密码 */
  password?: string;
  /** 连接字符串 */
  connectionString?: string;
}

/**
 * 数据库查询节点 (274)
 * 执行数据库查询
 */
export class DatabaseQueryNode extends AsyncNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const asyncOptions: AsyncNodeOptions = {
      ...options,
      inputFlowName: 'execute',
      outputFlowNames: ['success', 'error']
    };
    super(asyncOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'config',
      type: SocketType.DATA,
      dataType: 'object',
      description: '数据库连接配置',
      defaultValue: null
    });

    this.addInput({
      name: 'query',
      type: SocketType.DATA,
      dataType: 'string',
      description: 'SQL查询语句',
      defaultValue: ''
    });

    this.addInput({
      name: 'parameters',
      type: SocketType.DATA,
      dataType: 'array',
      description: '查询参数',
      defaultValue: []
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'array',
      description: '查询结果'
    });

    this.addOutput({
      name: 'rowCount',
      type: SocketType.DATA,
      dataType: 'number',
      description: '结果行数'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @returns Promise<any>
   */
  protected async executeAsync(): Promise<any> {
    const config = this.getInputValue('config') as DatabaseConfig;
    const query = this.getInputValue('query') as string;
    const parameters = this.getInputValue('parameters') as any[];

    // 验证输入
    if (!config) {
      throw new Error('数据库配置不能为空');
    }

    if (!query || typeof query !== 'string') {
      throw new Error('查询语句不能为空');
    }

    try {
      // 这里应该根据数据库类型创建连接并执行查询
      // 由于这是一个通用的视觉脚本节点，我们提供一个模拟实现
      const result = await this.simulateQuery(config, query, parameters);

      // 设置输出值
      this.setOutputValue('result', result);
      this.setOutputValue('rowCount', Array.isArray(result) ? result.length : 0);

      // 触发成功流程
      this.triggerFlow('success');
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '数据库查询失败';
      console.error('数据库查询失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('result', []);
      this.setOutputValue('rowCount', 0);

      // 触发错误流程
      this.triggerFlow('error');
      throw error;
    }
  }

  /**
   * 模拟数据库查询
   * @param config 数据库配置
   * @param query 查询语句
   * @param parameters 参数
   * @returns Promise<any[]>
   */
  private async simulateQuery(config: DatabaseConfig, query: string, parameters: any[]): Promise<any[]> {
    // 模拟异步查询延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // 这里应该实现真实的数据库连接和查询逻辑
    // 目前返回模拟数据
    console.log(`模拟数据库查询: ${query}`, parameters);
    
    return [
      { id: 1, name: '示例数据1', value: 'test1' },
      { id: 2, name: '示例数据2', value: 'test2' }
    ];
  }
}

/**
 * 数据库插入节点 (275)
 * 向数据库插入数据
 */
export class DatabaseInsertNode extends AsyncNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const asyncOptions: AsyncNodeOptions = {
      ...options,
      inputFlowName: 'execute',
      outputFlowNames: ['success', 'error']
    };
    super(asyncOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'config',
      type: SocketType.DATA,
      dataType: 'object',
      description: '数据库连接配置',
      defaultValue: null
    });

    this.addInput({
      name: 'table',
      type: SocketType.DATA,
      dataType: 'string',
      description: '表名',
      defaultValue: ''
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'object',
      description: '要插入的数据',
      defaultValue: {}
    });

    // 输出插槽
    this.addOutput({
      name: 'insertId',
      type: SocketType.DATA,
      dataType: 'number',
      description: '插入记录的ID'
    });

    this.addOutput({
      name: 'affectedRows',
      type: SocketType.DATA,
      dataType: 'number',
      description: '影响的行数'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @returns Promise<any>
   */
  protected async executeAsync(): Promise<any> {
    const config = this.getInputValue('config') as DatabaseConfig;
    const table = this.getInputValue('table') as string;
    const data = this.getInputValue('data') as object;

    // 验证输入
    if (!config) {
      throw new Error('数据库配置不能为空');
    }

    if (!table || typeof table !== 'string') {
      throw new Error('表名不能为空');
    }

    if (!data || typeof data !== 'object') {
      throw new Error('插入数据不能为空');
    }

    try {
      // 模拟插入操作
      const result = await this.simulateInsert(config, table, data);

      // 设置输出值
      this.setOutputValue('insertId', result.insertId);
      this.setOutputValue('affectedRows', result.affectedRows);

      // 触发成功流程
      this.triggerFlow('success');
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '数据库插入失败';
      console.error('数据库插入失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('insertId', 0);
      this.setOutputValue('affectedRows', 0);

      // 触发错误流程
      this.triggerFlow('error');
      throw error;
    }
  }

  /**
   * 模拟数据库插入
   * @param config 数据库配置
   * @param table 表名
   * @param data 数据
   * @returns Promise<{insertId: number, affectedRows: number}>
   */
  private async simulateInsert(config: DatabaseConfig, table: string, data: object): Promise<{insertId: number, affectedRows: number}> {
    // 模拟异步插入延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // 这里应该实现真实的数据库插入逻辑
    console.log(`模拟数据库插入到表 ${table}:`, data);
    
    return {
      insertId: Math.floor(Math.random() * 1000) + 1,
      affectedRows: 1
    };
  }
}

/**
 * 数据库更新节点 (276)
 * 更新数据库中的数据
 */
export class DatabaseUpdateNode extends AsyncNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const asyncOptions: AsyncNodeOptions = {
      ...options,
      inputFlowName: 'execute',
      outputFlowNames: ['success', 'error']
    };
    super(asyncOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'config',
      type: SocketType.DATA,
      dataType: 'object',
      description: '数据库连接配置',
      defaultValue: null
    });

    this.addInput({
      name: 'table',
      type: SocketType.DATA,
      dataType: 'string',
      description: '表名',
      defaultValue: ''
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'object',
      description: '要更新的数据',
      defaultValue: {}
    });

    this.addInput({
      name: 'where',
      type: SocketType.DATA,
      dataType: 'object',
      description: '更新条件',
      defaultValue: {}
    });

    // 输出插槽
    this.addOutput({
      name: 'affectedRows',
      type: SocketType.DATA,
      dataType: 'number',
      description: '影响的行数'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @returns Promise<any>
   */
  protected async executeAsync(): Promise<any> {
    const config = this.getInputValue('config') as DatabaseConfig;
    const table = this.getInputValue('table') as string;
    const data = this.getInputValue('data') as object;
    const where = this.getInputValue('where') as object;

    // 验证输入
    if (!config) {
      throw new Error('数据库配置不能为空');
    }

    if (!table || typeof table !== 'string') {
      throw new Error('表名不能为空');
    }

    if (!data || typeof data !== 'object') {
      throw new Error('更新数据不能为空');
    }

    try {
      // 模拟更新操作
      const result = await this.simulateUpdate(config, table, data, where);

      // 设置输出值
      this.setOutputValue('affectedRows', result.affectedRows);

      // 触发成功流程
      this.triggerFlow('success');
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '数据库更新失败';
      console.error('数据库更新失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('affectedRows', 0);

      // 触发错误流程
      this.triggerFlow('error');
      throw error;
    }
  }

  /**
   * 模拟数据库更新
   * @param config 数据库配置
   * @param table 表名
   * @param data 数据
   * @param where 条件
   * @returns Promise<{affectedRows: number}>
   */
  private async simulateUpdate(config: DatabaseConfig, table: string, data: object, where: object): Promise<{affectedRows: number}> {
    // 模拟异步更新延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // 这里应该实现真实的数据库更新逻辑
    console.log(`模拟数据库更新表 ${table}:`, { data, where });

    return {
      affectedRows: Math.floor(Math.random() * 5) + 1
    };
  }
}

/**
 * 数据库删除节点 (277)
 * 从数据库中删除数据
 */
export class DatabaseDeleteNode extends AsyncNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const asyncOptions: AsyncNodeOptions = {
      ...options,
      inputFlowName: 'execute',
      outputFlowNames: ['success', 'error']
    };
    super(asyncOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'config',
      type: SocketType.DATA,
      dataType: 'object',
      description: '数据库连接配置',
      defaultValue: null
    });

    this.addInput({
      name: 'table',
      type: SocketType.DATA,
      dataType: 'string',
      description: '表名',
      defaultValue: ''
    });

    this.addInput({
      name: 'where',
      type: SocketType.DATA,
      dataType: 'object',
      description: '删除条件',
      defaultValue: {}
    });

    // 输出插槽
    this.addOutput({
      name: 'affectedRows',
      type: SocketType.DATA,
      dataType: 'number',
      description: '影响的行数'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @returns Promise<any>
   */
  protected async executeAsync(): Promise<any> {
    const config = this.getInputValue('config') as DatabaseConfig;
    const table = this.getInputValue('table') as string;
    const where = this.getInputValue('where') as object;

    // 验证输入
    if (!config) {
      throw new Error('数据库配置不能为空');
    }

    if (!table || typeof table !== 'string') {
      throw new Error('表名不能为空');
    }

    if (!where || typeof where !== 'object' || Object.keys(where).length === 0) {
      throw new Error('删除条件不能为空（安全考虑）');
    }

    try {
      // 模拟删除操作
      const result = await this.simulateDelete(config, table, where);

      // 设置输出值
      this.setOutputValue('affectedRows', result.affectedRows);

      // 触发成功流程
      this.triggerFlow('success');
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '数据库删除失败';
      console.error('数据库删除失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('affectedRows', 0);

      // 触发错误流程
      this.triggerFlow('error');
      throw error;
    }
  }

  /**
   * 模拟数据库删除
   * @param config 数据库配置
   * @param table 表名
   * @param where 条件
   * @returns Promise<{affectedRows: number}>
   */
  private async simulateDelete(config: DatabaseConfig, table: string, where: object): Promise<{affectedRows: number}> {
    // 模拟异步删除延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // 这里应该实现真实的数据库删除逻辑
    console.log(`模拟数据库删除表 ${table} 数据:`, where);

    return {
      affectedRows: Math.floor(Math.random() * 3) + 1
    };
  }
}

/**
 * 注册数据库操作节点
 * @param registry 节点注册表
 */
export function registerDatabaseNodes(registry: NodeRegistry): void {
  // 注册数据库查询节点
  registry.registerNodeType({
    type: 'data/database/query',
    category: NodeCategory.DATA,
    constructor: DatabaseQueryNode,
    label: '数据库查询',
    description: '执行数据库查询',
    icon: 'database',
    color: '#2196F3',
    tags: ['data', 'database', 'query', 'select']
  });

  // 注册数据库插入节点
  registry.registerNodeType({
    type: 'data/database/insert',
    category: NodeCategory.DATA,
    constructor: DatabaseInsertNode,
    label: '数据库插入',
    description: '向数据库插入数据',
    icon: 'database',
    color: '#4CAF50',
    tags: ['data', 'database', 'insert', 'create']
  });

  // 注册数据库更新节点
  registry.registerNodeType({
    type: 'data/database/update',
    category: NodeCategory.DATA,
    constructor: DatabaseUpdateNode,
    label: '数据库更新',
    description: '更新数据库中的数据',
    icon: 'database',
    color: '#FF9800',
    tags: ['data', 'database', 'update', 'modify']
  });

  // 注册数据库删除节点
  registry.registerNodeType({
    type: 'data/database/delete',
    category: NodeCategory.DATA,
    constructor: DatabaseDeleteNode,
    label: '数据库删除',
    description: '从数据库中删除数据',
    icon: 'database',
    color: '#F44336',
    tags: ['data', 'database', 'delete', 'remove']
  });

  console.log('已注册所有数据库操作节点类型');
}
