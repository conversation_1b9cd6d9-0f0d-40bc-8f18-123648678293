# 数据存储节点使用示例 (271-285)

本文档介绍新实现的15个数据存储、文件操作和资产管理节点的使用方法和示例。

## 节点概览

| 编号 | 节点类型 | 中文名称 | 分类 | 描述 |
|------|----------|----------|------|------|
| 271 | data/localStorage/remove | 本地存储删除 | 数据存储 | 从本地存储中删除数据 |
| 272 | data/sessionStorage/set | 会话存储设置 | 数据存储 | 在会话存储中设置数据 |
| 273 | data/sessionStorage/get | 会话存储获取 | 数据存储 | 从会话存储中获取数据 |
| 274 | data/database/query | 数据库查询 | 数据存储 | 执行数据库查询 |
| 275 | data/database/insert | 数据库插入 | 数据存储 | 向数据库插入数据 |
| 276 | data/database/update | 数据库更新 | 数据存储 | 更新数据库中的数据 |
| 277 | data/database/delete | 数据库删除 | 数据存储 | 从数据库中删除数据 |
| 278 | file/load | 加载文件 | 文件操作 | 加载指定路径的文件 |
| 279 | file/save | 保存文件 | 文件操作 | 保存数据到文件 |
| 280 | file/exists | 文件存在检查 | 文件操作 | 检查文件是否存在 |
| 281 | file/getSize | 获取文件大小 | 文件操作 | 获取文件的大小 |
| 282 | file/getExtension | 获取文件扩展名 | 文件操作 | 获取文件的扩展名 |
| 283 | asset/load | 加载资产 | 资产管理 | 加载指定的资产文件 |
| 284 | asset/unload | 卸载资产 | 资产管理 | 卸载已加载的资产 |
| 285 | asset/getProgress | 获取加载进度 | 资产管理 | 获取资产加载进度 |

## 本地存储节点

### 本地存储删除节点 (271)

**功能**: 从浏览器的localStorage中删除指定键的数据。

**输入参数**:
- `key` (string): 要删除的键名

**输出结果**:
- `key` (string): 被删除的键名
- `existed` (boolean): 键是否存在
- `success` (boolean): 删除是否成功
- `error` (string): 错误信息

**使用示例**:
```
[开始] → [本地存储删除] → [删除成功] → [显示提示]
           ↓
       [删除失败] → [显示错误]
```

## 会话存储节点

### 会话存储设置节点 (272)

**功能**: 在浏览器的sessionStorage中设置数据。

**输入参数**:
- `key` (string): 存储的键名
- `value` (any): 要存储的值
- `serialize` (boolean): 是否序列化为JSON

**输出结果**:
- `key` (string): 存储的键名
- `value` (any): 存储的值
- `success` (boolean): 存储是否成功
- `error` (string): 错误信息

### 会话存储获取节点 (273)

**功能**: 从浏览器的sessionStorage中获取数据。

**输入参数**:
- `key` (string): 要获取的键名
- `defaultValue` (any): 默认值
- `deserialize` (boolean): 是否反序列化JSON

**输出结果**:
- `value` (any): 获取的值
- `rawValue` (string): 原始字符串值
- `exists` (boolean): 键是否存在
- `error` (string): 错误信息

**使用示例**:
```
[页面加载] → [会话存储获取] → [找到数据] → [恢复状态]
                           ↓
                       [未找到] → [使用默认值]
```

## 数据库操作节点

### 数据库查询节点 (274)

**功能**: 执行数据库查询操作。

**输入参数**:
- `config` (object): 数据库连接配置
- `query` (string): SQL查询语句
- `parameters` (array): 查询参数

**输出结果**:
- `result` (array): 查询结果
- `rowCount` (number): 结果行数
- `error` (string): 错误信息

### 数据库插入节点 (275)

**功能**: 向数据库插入数据。

**输入参数**:
- `config` (object): 数据库连接配置
- `table` (string): 表名
- `data` (object): 要插入的数据

**输出结果**:
- `insertId` (number): 插入记录的ID
- `affectedRows` (number): 影响的行数
- `error` (string): 错误信息

**使用示例**:
```
[用户注册] → [数据库插入] → [插入成功] → [返回用户ID]
                         ↓
                     [插入失败] → [显示错误]
```

## 文件操作节点

### 加载文件节点 (278)

**功能**: 加载指定路径的文件内容。

**输入参数**:
- `path` (string): 文件路径
- `encoding` (string): 文件编码
- `asDataURL` (boolean): 是否作为DataURL读取

**输出结果**:
- `content` (string): 文件内容
- `size` (number): 文件大小
- `lastModified` (number): 最后修改时间
- `error` (string): 错误信息

### 保存文件节点 (279)

**功能**: 保存数据到文件。

**输入参数**:
- `path` (string): 文件路径
- `content` (string): 文件内容
- `encoding` (string): 文件编码
- `createDir` (boolean): 是否创建目录

**输出结果**:
- `path` (string): 保存的文件路径
- `size` (number): 保存的文件大小
- `success` (boolean): 保存是否成功
- `error` (string): 错误信息

### 文件存在检查节点 (280)

**功能**: 检查文件是否存在。

**输入参数**:
- `path` (string): 文件路径

**输出结果**:
- `exists` (boolean): 文件是否存在
- `error` (string): 错误信息

### 获取文件扩展名节点 (282)

**功能**: 解析文件路径，获取扩展名和其他信息。

**输入参数**:
- `path` (string): 文件路径
- `includeDot` (boolean): 是否包含点号

**输出结果**:
- `extension` (string): 文件扩展名
- `filename` (string): 文件名（不含扩展名）
- `basename` (string): 完整文件名
- `dirname` (string): 目录路径

**使用示例**:
```
[文件路径] → [获取文件扩展名] → [扩展名: .jpg]
                              → [文件名: image]
                              → [目录: /uploads]
```

## 资产管理节点

### 加载资产节点 (283)

**功能**: 加载游戏资产文件（纹理、模型、音频等）。

**输入参数**:
- `path` (string): 资产路径
- `type` (string): 资产类型
- `preload` (boolean): 是否预加载
- `cache` (boolean): 是否缓存

**输出结果**:
- `asset` (object): 加载的资产对象
- `assetId` (string): 资产ID
- `size` (number): 资产大小
- `loadTime` (number): 加载时间
- `error` (string): 错误信息

### 卸载资产节点 (284)

**功能**: 卸载已加载的资产，释放内存。

**输入参数**:
- `assetId` (string): 资产ID
- `path` (string): 资产路径（可选）
- `force` (boolean): 是否强制卸载

**输出结果**:
- `unloaded` (boolean): 是否成功卸载
- `freedMemory` (number): 释放的内存大小
- `error` (string): 错误信息

### 获取加载进度节点 (285)

**功能**: 获取资产加载进度信息。

**输入参数**:
- `assetId` (string): 资产ID
- `path` (string): 资产路径（可选）

**输出结果**:
- `progress` (number): 加载进度（0-100）
- `loaded` (boolean): 是否已加载完成
- `loadedBytes` (number): 已加载字节数
- `totalBytes` (number): 总字节数
- `status` (string): 加载状态
- `error` (string): 错误信息

**使用示例**:
```
[开始加载] → [加载资产] → [获取进度] → [更新进度条]
                                    ↓
                                [加载完成] → [使用资产]
```

## 综合应用示例

### 游戏存档系统

```
[游戏数据] → [JSON序列化] → [本地存储设置] → [存档成功]
                                        ↓
[游戏启动] → [本地存储获取] → [JSON解析] → [恢复游戏状态]
```

### 资产管理系统

```
[资产列表] → [加载资产] → [获取进度] → [更新UI]
                       ↓
                   [加载完成] → [缓存资产ID]
                              ↓
[游戏结束] → [卸载资产] → [释放内存]
```

### 配置文件管理

```
[配置数据] → [保存文件] → [文件存在检查] → [确认保存]
                                        ↓
[应用启动] → [加载文件] → [解析配置] → [应用设置]
```

## 注意事项

1. **浏览器兼容性**: localStorage和sessionStorage在某些环境中可能不可用
2. **数据库连接**: 数据库节点需要正确的连接配置
3. **文件权限**: 文件操作需要适当的读写权限
4. **内存管理**: 及时卸载不需要的资产以释放内存
5. **错误处理**: 始终检查错误输出并进行适当处理

## 最佳实践

1. **数据验证**: 在存储前验证数据格式和内容
2. **异常处理**: 使用错误流程处理异常情况
3. **性能优化**: 合理使用缓存和预加载
4. **安全考虑**: 避免存储敏感信息到本地存储
5. **资源清理**: 及时清理不需要的数据和资产
