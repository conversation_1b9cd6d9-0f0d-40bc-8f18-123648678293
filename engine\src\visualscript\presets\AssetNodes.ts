/**
 * 视觉脚本资产管理节点
 * 提供资产加载、卸载、进度查询功能
 */
import { AsyncNode, AsyncNodeOptions } from '../nodes/AsyncNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType, NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 资产类型枚举
 */
export enum AssetType {
  TEXTURE = 'texture',
  MODEL = 'model',
  AUDIO = 'audio',
  VIDEO = 'video',
  FONT = 'font',
  SCRIPT = 'script',
  DATA = 'data',
  UNKNOWN = 'unknown'
}

/**
 * 资产信息接口
 */
export interface AssetInfo {
  /** 资产ID */
  id: string;
  /** 资产路径 */
  path: string;
  /** 资产类型 */
  type: AssetType;
  /** 资产大小 */
  size: number;
  /** 是否已加载 */
  loaded: boolean;
  /** 加载进度 */
  progress: number;
  /** 资产数据 */
  data?: any;
}

/**
 * 加载资产节点 (283)
 * 加载指定的资产文件
 */
export class LoadAssetNode extends AsyncNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const asyncOptions: AsyncNodeOptions = {
      ...options,
      inputFlowName: 'load',
      outputFlowNames: ['success', 'error']
    };
    super(asyncOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'path',
      type: SocketType.DATA,
      dataType: 'string',
      description: '资产路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'type',
      type: SocketType.DATA,
      dataType: 'string',
      description: '资产类型',
      defaultValue: 'unknown'
    });

    this.addInput({
      name: 'preload',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否预加载',
      defaultValue: false
    });

    this.addInput({
      name: 'cache',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否缓存',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'asset',
      type: SocketType.DATA,
      dataType: 'object',
      description: '加载的资产对象'
    });

    this.addOutput({
      name: 'assetId',
      type: SocketType.DATA,
      dataType: 'string',
      description: '资产ID'
    });

    this.addOutput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      description: '资产大小'
    });

    this.addOutput({
      name: 'loadTime',
      type: SocketType.DATA,
      dataType: 'number',
      description: '加载时间（毫秒）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @returns Promise<any>
   */
  protected async executeAsync(): Promise<any> {
    const path = this.getInputValue('path') as string;
    const type = this.getInputValue('type') as string;
    const preload = this.getInputValue('preload') as boolean;
    const cache = this.getInputValue('cache') as boolean;

    // 验证输入
    if (!path || typeof path !== 'string') {
      throw new Error('资产路径不能为空');
    }

    const startTime = Date.now();

    try {
      // 加载资产
      const assetInfo = await this.loadAsset(path, type as AssetType, preload, cache);
      const loadTime = Date.now() - startTime;

      // 设置输出值
      this.setOutputValue('asset', assetInfo.data);
      this.setOutputValue('assetId', assetInfo.id);
      this.setOutputValue('size', assetInfo.size);
      this.setOutputValue('loadTime', loadTime);

      // 触发成功流程
      this.triggerFlow('success');
      return assetInfo.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '资产加载失败';
      console.error('资产加载失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('asset', null);
      this.setOutputValue('assetId', '');
      this.setOutputValue('size', 0);
      this.setOutputValue('loadTime', Date.now() - startTime);

      // 触发错误流程
      this.triggerFlow('error');
      throw error;
    }
  }

  /**
   * 加载资产
   * @param path 资产路径
   * @param type 资产类型
   * @param preload 是否预加载
   * @param cache 是否缓存
   * @returns Promise<AssetInfo>
   */
  private async loadAsset(path: string, type: AssetType, preload: boolean, cache: boolean): Promise<AssetInfo> {
    // 生成资产ID
    const assetId = this.generateAssetId(path);
    
    // 检测资产类型
    const detectedType = type === AssetType.UNKNOWN ? this.detectAssetType(path) : type;
    
    // 模拟资产加载过程
    console.log(`开始加载资产: ${path}, 类型: ${detectedType}, 预加载: ${preload}, 缓存: ${cache}`);
    
    // 模拟加载延迟
    const loadDelay = Math.random() * 1000 + 500; // 500-1500ms
    await new Promise(resolve => setTimeout(resolve, loadDelay));
    
    // 创建模拟资产数据
    const assetData = this.createMockAssetData(detectedType, path);
    
    const assetInfo: AssetInfo = {
      id: assetId,
      path,
      type: detectedType,
      size: Math.floor(Math.random() * 1024 * 1024) + 1024, // 1KB-1MB
      loaded: true,
      progress: 100,
      data: assetData
    };
    
    return assetInfo;
  }

  /**
   * 生成资产ID
   * @param path 资产路径
   * @returns string
   */
  private generateAssetId(path: string): string {
    // 简单的哈希函数生成ID
    let hash = 0;
    for (let i = 0; i < path.length; i++) {
      const char = path.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return `asset_${Math.abs(hash).toString(16)}`;
  }

  /**
   * 检测资产类型
   * @param path 资产路径
   * @returns AssetType
   */
  private detectAssetType(path: string): AssetType {
    const extension = path.split('.').pop()?.toLowerCase() || '';
    
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return AssetType.TEXTURE;
      
      case 'obj':
      case 'fbx':
      case 'gltf':
      case 'glb':
      case '3ds':
        return AssetType.MODEL;
      
      case 'mp3':
      case 'wav':
      case 'ogg':
      case 'aac':
        return AssetType.AUDIO;
      
      case 'mp4':
      case 'webm':
      case 'avi':
      case 'mov':
        return AssetType.VIDEO;
      
      case 'ttf':
      case 'otf':
      case 'woff':
      case 'woff2':
        return AssetType.FONT;
      
      case 'js':
      case 'ts':
      case 'lua':
        return AssetType.SCRIPT;
      
      case 'json':
      case 'xml':
      case 'csv':
        return AssetType.DATA;
      
      default:
        return AssetType.UNKNOWN;
    }
  }

  /**
   * 创建模拟资产数据
   * @param type 资产类型
   * @param path 资产路径
   * @returns any
   */
  private createMockAssetData(type: AssetType, path: string): any {
    switch (type) {
      case AssetType.TEXTURE:
        return {
          width: 512,
          height: 512,
          format: 'RGBA',
          url: path
        };
      
      case AssetType.MODEL:
        return {
          vertices: 1000,
          faces: 500,
          materials: 2,
          url: path
        };
      
      case AssetType.AUDIO:
        return {
          duration: 120.5,
          sampleRate: 44100,
          channels: 2,
          url: path
        };
      
      case AssetType.VIDEO:
        return {
          width: 1920,
          height: 1080,
          duration: 300.2,
          fps: 30,
          url: path
        };
      
      case AssetType.FONT:
        return {
          family: 'CustomFont',
          style: 'normal',
          weight: 400,
          url: path
        };
      
      case AssetType.SCRIPT:
        return {
          language: 'javascript',
          size: 1024,
          url: path
        };
      
      case AssetType.DATA:
        return {
          format: 'json',
          records: 100,
          url: path
        };
      
      default:
        return {
          type: 'unknown',
          url: path
        };
    }
  }
}

/**
 * 卸载资产节点 (284)
 * 卸载已加载的资产
 */
export class UnloadAssetNode extends AsyncNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const asyncOptions: AsyncNodeOptions = {
      ...options,
      inputFlowName: 'unload',
      outputFlowNames: ['success', 'error']
    };
    super(asyncOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'assetId',
      type: SocketType.DATA,
      dataType: 'string',
      description: '资产ID',
      defaultValue: ''
    });

    this.addInput({
      name: 'path',
      type: SocketType.DATA,
      dataType: 'string',
      description: '资产路径（可选）',
      defaultValue: ''
    });

    this.addInput({
      name: 'force',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否强制卸载',
      defaultValue: false
    });

    // 输出插槽
    this.addOutput({
      name: 'unloaded',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否成功卸载'
    });

    this.addOutput({
      name: 'freedMemory',
      type: SocketType.DATA,
      dataType: 'number',
      description: '释放的内存大小（字节）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @returns Promise<any>
   */
  protected async executeAsync(): Promise<any> {
    const assetId = this.getInputValue('assetId') as string;
    const path = this.getInputValue('path') as string;
    const force = this.getInputValue('force') as boolean;

    // 验证输入
    if (!assetId && !path) {
      throw new Error('资产ID或路径不能为空');
    }

    try {
      // 卸载资产
      const result = await this.unloadAsset(assetId || path, force);

      // 设置输出值
      this.setOutputValue('unloaded', result.unloaded);
      this.setOutputValue('freedMemory', result.freedMemory);

      // 触发成功流程
      this.triggerFlow('success');
      return result.unloaded;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '资产卸载失败';
      console.error('资产卸载失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('unloaded', false);
      this.setOutputValue('freedMemory', 0);

      // 触发错误流程
      this.triggerFlow('error');
      throw error;
    }
  }

  /**
   * 卸载资产
   * @param identifier 资产标识符（ID或路径）
   * @param force 是否强制卸载
   * @returns Promise<{unloaded: boolean, freedMemory: number}>
   */
  private async unloadAsset(identifier: string, force: boolean): Promise<{unloaded: boolean, freedMemory: number}> {
    console.log(`开始卸载资产: ${identifier}, 强制卸载: ${force}`);

    // 模拟卸载延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // 模拟释放的内存大小
    const freedMemory = Math.floor(Math.random() * 1024 * 1024) + 1024; // 1KB-1MB

    return {
      unloaded: true,
      freedMemory
    };
  }
}

/**
 * 获取加载进度节点 (285)
 * 获取资产加载进度
 */
export class GetAssetProgressNode extends FunctionNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'assetId',
      type: SocketType.DATA,
      dataType: 'string',
      description: '资产ID',
      defaultValue: ''
    });

    this.addInput({
      name: 'path',
      type: SocketType.DATA,
      dataType: 'string',
      description: '资产路径（可选）',
      defaultValue: ''
    });

    // 输出插槽
    this.addOutput({
      name: 'progress',
      type: SocketType.DATA,
      dataType: 'number',
      description: '加载进度（0-100）'
    });

    this.addOutput({
      name: 'loaded',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否已加载完成'
    });

    this.addOutput({
      name: 'loadedBytes',
      type: SocketType.DATA,
      dataType: 'number',
      description: '已加载字节数'
    });

    this.addOutput({
      name: 'totalBytes',
      type: SocketType.DATA,
      dataType: 'number',
      description: '总字节数'
    });

    this.addOutput({
      name: 'status',
      type: SocketType.DATA,
      dataType: 'string',
      description: '加载状态'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const assetId = this.getInputValue('assetId') as string;
    const path = this.getInputValue('path') as string;

    // 验证输入
    if (!assetId && !path) {
      this.setOutputValue('error', '资产ID或路径不能为空');
      this.setOutputValue('progress', 0);
      this.setOutputValue('loaded', false);
      this.setOutputValue('loadedBytes', 0);
      this.setOutputValue('totalBytes', 0);
      this.setOutputValue('status', 'error');
      return 0;
    }

    try {
      // 获取资产进度
      const progressInfo = this.getAssetProgress(assetId || path);

      // 设置输出值
      this.setOutputValue('progress', progressInfo.progress);
      this.setOutputValue('loaded', progressInfo.loaded);
      this.setOutputValue('loadedBytes', progressInfo.loadedBytes);
      this.setOutputValue('totalBytes', progressInfo.totalBytes);
      this.setOutputValue('status', progressInfo.status);
      this.setOutputValue('error', '');

      return progressInfo.progress;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取进度失败';
      console.error('获取资产进度失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('progress', 0);
      this.setOutputValue('loaded', false);
      this.setOutputValue('loadedBytes', 0);
      this.setOutputValue('totalBytes', 0);
      this.setOutputValue('status', 'error');

      return 0;
    }
  }

  /**
   * 获取资产进度
   * @param identifier 资产标识符
   * @returns 进度信息
   */
  private getAssetProgress(identifier: string): {progress: number, loaded: boolean, loadedBytes: number, totalBytes: number, status: string} {
    console.log(`获取资产进度: ${identifier}`);

    // 模拟进度信息
    const totalBytes = Math.floor(Math.random() * 1024 * 1024) + 1024; // 1KB-1MB
    const progress = Math.floor(Math.random() * 101); // 0-100
    const loadedBytes = Math.floor((totalBytes * progress) / 100);
    const loaded = progress === 100;

    let status: string;
    if (progress === 0) {
      status = 'pending';
    } else if (progress === 100) {
      status = 'completed';
    } else {
      status = 'loading';
    }

    return {
      progress,
      loaded,
      loadedBytes,
      totalBytes,
      status
    };
  }
}

/**
 * 注册资产管理节点
 * @param registry 节点注册表
 */
export function registerAssetNodes(registry: NodeRegistry): void {
  // 注册加载资产节点
  registry.registerNodeType({
    type: 'asset/load',
    category: NodeCategory.DATA,
    constructor: LoadAssetNode,
    label: '加载资产',
    description: '加载指定的资产文件',
    icon: 'download',
    color: '#4CAF50',
    tags: ['asset', 'load', 'resource', 'data']
  });

  // 注册卸载资产节点
  registry.registerNodeType({
    type: 'asset/unload',
    category: NodeCategory.DATA,
    constructor: UnloadAssetNode,
    label: '卸载资产',
    description: '卸载已加载的资产',
    icon: 'delete',
    color: '#F44336',
    tags: ['asset', 'unload', 'resource', 'memory']
  });

  // 注册获取加载进度节点
  registry.registerNodeType({
    type: 'asset/getProgress',
    category: NodeCategory.DATA,
    constructor: GetAssetProgressNode,
    label: '获取加载进度',
    description: '获取资产加载进度',
    icon: 'progress',
    color: '#2196F3',
    tags: ['asset', 'progress', 'loading', 'status']
  });

  console.log('已注册所有资产管理节点类型');
}
